import React, { useState } from 'react';
import { Search, Filter, Heart, ShoppingBag, Menu, Bot } from 'lucide-react';
import { products } from '../../data/products';
import ProductCard from '../Product/ProductCard';
import { Product } from '../../types/product';
import { useCart } from '../../contexts/CartContext';
import { useTelegram } from '../../hooks/useTelegram';

interface HomeProps {
  onProductSelect: (product: Product) => void;
  onGoToCart: () => void;
  onGoToWishlist: () => void;
}

const Home: React.FC<HomeProps> = ({ onProductSelect, onGoToCart, onGoToWishlist }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showMenu, setShowMenu] = useState(false);
  const { state } = useCart();
  const { hapticFeedback, showAlert } = useTelegram();
  
  const newArrivals = products.slice(0, 4);
  const filteredProducts = newArrivals.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleMenuToggle = () => {
    hapticFeedback.light();
    setShowMenu(!showMenu);
  };

  const handleCartClick = () => {
    hapticFeedback.medium();
    onGoToCart();
  };

  const handleWishlistClick = () => {
    hapticFeedback.medium();
    onGoToWishlist();
  };

  const handleAIAssistant = () => {
    hapticFeedback.medium();
    showAlert('AI Assistant: Hi! I can help you find the perfect sunglasses. What style are you looking for?');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col animate-fade-in relative">
      {/* Main Content */}
      <div className="flex-1 px-6 pt-16 pb-32 overflow-y-auto">
        {/* Header with New Arrivals */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-baseline space-x-3">
            <span className="text-7xl font-black text-gray-900 font-display tracking-tighter">16</span>
            <div className="text-lg font-semibold text-gray-900 leading-tight tracking-tight">
              <div>New</div>
              <div>Arrivals</div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button 
              onClick={handleWishlistClick}
              className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
            >
              <Heart className="w-5 h-5 text-gray-600" />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200">
              <Filter className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="relative mb-10">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search your product"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-12 pr-4 py-4 bg-white rounded-2xl text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500/20 border border-gray-100 shadow-soft transition-all duration-200"
          />
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-2 gap-6">
          {filteredProducts.map((product, index) => (
            <div 
              key={product.id}
              className="animate-slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <ProductCard
                product={product}
                onSelect={onProductSelect}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Fixed Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 px-6 py-6 bg-white/95 border-t border-gray-100/50 backdrop-blur-xl z-30">
        <div className="flex items-center justify-between">
          {/* Menu dots */}
          <button 
            onClick={handleMenuToggle}
            className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
          >
            <Menu className="w-6 h-6 text-gray-900" />
          </button>

          {/* Luxora Brand */}
          <h1 className="text-2xl font-bold text-gray-900 tracking-tight font-display">Luxora</h1>

          {/* Cart Icon with Badge */}
          <button 
            onClick={handleCartClick}
            className="relative p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
          >
            <ShoppingBag className="w-6 h-6 text-gray-900" />
            {state.items.length > 0 && (
              <div className="absolute -top-1 -right-1 w-5 h-5 bg-pink-500 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-xs text-white font-semibold">{state.items.length}</span>
              </div>
            )}
          </button>
        </div>
      </div>

      {/* Hamburger Menu Overlay */}
      {showMenu && (
        <div className="fixed inset-0 bg-black/50 z-40 animate-fade-in" onClick={() => setShowMenu(false)}>
          <div className="fixed left-0 top-0 bottom-0 w-80 bg-white shadow-xl animate-slide-right">
            <div className="p-6">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-bold text-gray-900 tracking-tight">Menu</h2>
                <button 
                  onClick={() => setShowMenu(false)}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
                >
                  <span className="text-2xl text-gray-600">×</span>
                </button>
              </div>
              
              <nav className="space-y-4">
                <button 
                  onClick={() => {
                    handleWishlistClick();
                    setShowMenu(false);
                  }}
                  className="w-full flex items-center space-x-4 p-4 hover:bg-gray-50 rounded-xl transition-all duration-200"
                >
                  <Heart className="w-6 h-6 text-gray-600" />
                  <span className="text-lg font-medium text-gray-900">Wishlist</span>
                </button>
                
                <button 
                  onClick={() => {
                    handleCartClick();
                    setShowMenu(false);
                  }}
                  className="w-full flex items-center space-x-4 p-4 hover:bg-gray-50 rounded-xl transition-all duration-200"
                >
                  <ShoppingBag className="w-6 h-6 text-gray-600" />
                  <span className="text-lg font-medium text-gray-900">Shopping Cart</span>
                  {state.items.length > 0 && (
                    <span className="ml-auto bg-pink-500 text-white text-sm px-2 py-1 rounded-full">
                      {state.items.length}
                    </span>
                  )}
                </button>
                
                <button className="w-full flex items-center space-x-4 p-4 hover:bg-gray-50 rounded-xl transition-all duration-200">
                  <Search className="w-6 h-6 text-gray-600" />
                  <span className="text-lg font-medium text-gray-900">Search</span>
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* AI Assistant Floating Button */}
      <button
        onClick={handleAIAssistant}
        className="fixed bottom-28 right-6 w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 transform hover:scale-110 z-20"
      >
        <Bot className="w-6 h-6" />
      </button>
    </div>
  );
};

export default Home;