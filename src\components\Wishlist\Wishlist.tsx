import React, { useState, useEffect } from 'react';
import { ArrowLeft, Heart, ShoppingBag, Trash2 } from 'lucide-react';
import { Product } from '../../types/product';
import { products } from '../../data/products';
import { useTelegram } from '../../hooks/useTelegram';
import { useCart } from '../../contexts/CartContext';

interface WishlistProps {
  onBack: () => void;
  onProductSelect: (product: Product) => void;
}

const Wishlist: React.FC<WishlistProps> = ({ onBack, onProductSelect }) => {
  const [wishlistItems, setWishlistItems] = useState<Product[]>([]);
  const { hapticFeedback, cloudStorage, showConfirm } = useTelegram();
  const { dispatch } = useCart();

  useEffect(() => {
    loadWishlist();
  }, []);

  const loadWishlist = async () => {
    const savedWishlist = await cloudStorage.getItem('wishlist');
    if (savedWishlist) {
      const wishlistIds = JSON.parse(savedWishlist);
      const wishlistProducts = products.filter(product => wishlistIds.includes(product.id));
      setWishlistItems(wishlistProducts);
    }
  };

  const removeFromWishlist = async (productId: string) => {
    hapticFeedback.warning();
    const confirmed = await showConfirm('Remove this item from your wishlist?');
    
    if (confirmed) {
      const updatedItems = wishlistItems.filter(item => item.id !== productId);
      setWishlistItems(updatedItems);
      
      const wishlistIds = updatedItems.map(item => item.id);
      await cloudStorage.setItem('wishlist', JSON.stringify(wishlistIds));
      
      hapticFeedback.success();
    }
  };

  const addToCart = (product: Product) => {
    hapticFeedback.medium();
    dispatch({ type: 'ADD_ITEM', payload: { product } });
    hapticFeedback.success();
  };

  return (
    <div className="min-h-screen bg-gray-50 animate-fade-in">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl px-6 pt-12 pb-6 border-b border-gray-100/50">
        <div className="flex items-center justify-between">
          <button
            onClick={() => {
              hapticFeedback.light();
              onBack();
            }}
            className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
          >
            <ArrowLeft className="w-6 h-6 text-gray-700" />
          </button>
          <h1 className="text-xl font-semibold text-gray-900 tracking-tight">Wishlist</h1>
          <div className="w-10"></div>
        </div>
      </div>

      <div className="px-6 py-6">
        {wishlistItems.length === 0 ? (
          <div className="text-center py-24 animate-slide-up">
            <div className="w-24 h-24 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Heart className="w-12 h-12 text-pink-500" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-3 tracking-tight">Your wishlist is empty</h2>
            <p className="text-gray-500 text-lg mb-8">Save items you love to buy them later</p>
            <button
              onClick={() => {
                hapticFeedback.medium();
                onBack();
              }}
              className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-2xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              Start Shopping
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {wishlistItems.map((product, index) => (
              <div 
                key={product.id}
                className="bg-white rounded-3xl p-6 shadow-soft animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => {
                      hapticFeedback.light();
                      onProductSelect(product);
                    }}
                    className="flex-shrink-0"
                  >
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-20 h-20 object-cover rounded-2xl hover:scale-105 transition-transform duration-200"
                    />
                  </button>
                  
                  <div className="flex-1">
                    <button
                      onClick={() => {
                        hapticFeedback.light();
                        onProductSelect(product);
                      }}
                      className="text-left"
                    >
                      <h3 className="font-semibold text-gray-900 text-lg tracking-tight mb-1">
                        {product.name}
                      </h3>
                      <div className="flex items-center space-x-2 mb-3">
                        <span className="text-xl font-bold text-gray-900 tracking-tight">
                          ${product.price.toFixed(2)}
                        </span>
                        {product.originalPrice && (
                          <span className="text-sm text-gray-400 line-through font-medium">
                            ${product.originalPrice.toFixed(2)}
                          </span>
                        )}
                      </div>
                    </button>
                    
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => addToCart(product)}
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center space-x-2"
                      >
                        <ShoppingBag className="w-4 h-4" />
                        <span>Add to Cart</span>
                      </button>
                      
                      <button
                        onClick={() => removeFromWishlist(product.id)}
                        className="p-2 text-red-500 hover:bg-red-50 rounded-xl transition-all duration-200"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Wishlist;