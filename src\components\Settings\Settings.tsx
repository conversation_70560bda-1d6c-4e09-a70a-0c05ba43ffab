import React, { useState } from 'react';
import { ArrowLef<PERSON>, User, Bell, Shield, Palette } from 'lucide-react';
import { useTelegram } from '../../hooks/useTelegram';

interface SettingsProps {
  onBack: () => void;
}

interface SettingItem {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  value?: string | boolean;
  action?: () => void;
  toggle?: boolean;
  disabled?: boolean;
  destructive?: boolean;
}

const Settings: React.FC<SettingsProps> = ({ onBack }) => {
  const { user, showAlert } = useTelegram();
  const [notifications, setNotifications] = useState(true);
  const [theme, setTheme] = useState<'light' | 'dark' | 'auto'>('auto');

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'auto') => {
    setTheme(newTheme);
    showAlert(`Theme set to ${newTheme}`);
  };

  const handleNotificationsToggle = () => {
    setNotifications(!notifications);
    showAlert(`Notifications ${!notifications ? 'enabled' : 'disabled'}`);
  };

  const settings: SettingItem[] = [
    {
      icon: User,
      label: 'Profile',
      value: user?.username || 'Guest',
      action: () => {
        showAlert('Profile settings coming soon!');
      },
    },
    {
      icon: Bell,
      label: 'Notifications',
      toggle: true,
      value: notifications,
      action: handleNotificationsToggle,
    },
    {
      icon: Shield,
      label: 'Security',
      action: () => {
        showAlert('Security settings coming soon!');
      },
    },
    {
      icon: Palette,
      label: 'Theme',
      value: theme,
      action: () => {
        const themes: ('light' | 'dark' | 'auto')[] = ['light', 'dark', 'auto'];
        const currentIndex = themes.indexOf(theme);
        const nextIndex = (currentIndex + 1) % themes.length;
        handleThemeChange(themes[nextIndex]);
      },
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 pb-24">
      <div className="bg-white border-b border-gray-100 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={onBack}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              aria-label="Go back"
            >
              <ArrowLeft className="w-5 h-5 text-gray-700" />
            </button>
            <h1 className="text-lg font-semibold text-gray-900">Settings</h1>
          </div>
        </div>
      </div>

      <div className="px-4 py-6 space-y-4">
        <div className="space-y-2">
          {settings.map((setting, index) => (
            <div
              key={index}
              onClick={setting.action}
              className={`flex items-center justify-between p-4 bg-white rounded-lg shadow-xs cursor-pointer transition-colors ${
                setting.disabled ? 'opacity-50' : 'hover:bg-gray-50'
              } ${setting.destructive ? 'text-red-600' : 'text-gray-900'}`}
            >
              <div className="flex items-center space-x-3">
                <setting.icon
                  className={`w-5 h-5 ${setting.destructive ? 'text-red-600' : 'text-gray-500'}`}
                />
                <span className="font-medium">{setting.label}</span>
              </div>
              <div className="flex items-center">
                {setting.toggle ? (
                  <div className="relative inline-block w-10 mr-2 align-middle select-none">
                    <input
                      type="checkbox"
                      name={`toggle-${index}`}
                      id={`toggle-${index}`}
                      checked={setting.value as boolean}
                      onChange={setting.action}
                      className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                      disabled={setting.disabled}
                    />
                    <label
                      htmlFor={`toggle-${index}`}
                      className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${
                        setting.value ? 'bg-blue-500' : 'bg-gray-300'
                      }`}
                    ></label>
                  </div>
                ) : setting.value !== undefined ? (
                  <span className="text-sm text-gray-500 mr-2">
                    {setting.value.toString().charAt(0).toUpperCase() + setting.value.toString().slice(1)}
                  </span>
                ) : null}
                <svg
                  className="w-5 h-5 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Settings;