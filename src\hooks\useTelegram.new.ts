import { useEffect, useState, useCallback, useMemo } from 'react';

// Type definitions for Telegram WebApp
type ThemeParams = {
  bg_color?: string;
  text_color?: string;
  hint_color?: string;
  link_color?: string;
  button_color?: string;
  button_text_color?: string;
  secondary_bg_color?: string;
  accent_text_color?: string;
  section_bg_color?: string;
  section_header_text_color?: string;
  subtitle_text_color?: string;
  destructive_text_color?: string;
};

type HapticFeedback = {
  impactOccurred: (style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft') => void;
  notificationOccurred: (type: 'error' | 'success' | 'warning') => void;
  selectionChanged: () => void;
};

type BackButton = {
  isVisible: boolean;
  show: () => void;
  hide: () => void;
  onClick: (callback: () => void) => void;
  offClick: (callback: () => void) => void;
  showProgress: (show: boolean) => void;
};

type MainButton = {
  text: string;
  color: string;
  textColor: string;
  isVisible: boolean;
  isActive: boolean;
  isProgressVisible: boolean;
  setText: (text: string) => void;
  onClick: (callback: () => void) => void;
  offClick: (callback: () => void) => void;
  show: () => void;
  hide: () => void;
  enable: () => void;
  disable: () => void;
  showProgress: (leaveActive?: boolean) => void;
  hideProgress: () => void;
  setParams: (params: {
    color?: string;
    text_color?: string;
    is_active?: boolean;
    is_visible?: boolean;
    text?: string;
  }) => void;
};

type PopupButton = {
  id?: string;
  type?: 'default' | 'ok' | 'close' | 'cancel' | 'destructive';
  text?: string;
};

type PopupParams = {
  title?: string;
  message: string;
  buttons?: PopupButton[];
};

interface TelegramWebApp {
  // Core properties
  initData: string;
  initDataUnsafe: {
    user?: {
      id: number;
      first_name: string;
      last_name?: string;
      username?: string;
      language_code?: string;
      is_premium?: boolean;
      photo_url?: string;
      allows_write_to_pm?: boolean;
    };
    chat_type?: 'sender' | 'private' | 'group' | 'supergroup' | 'channel';
    chat_instance?: string;
    start_param?: string;
    can_send_after?: number;
    auth_date: number;
    hash: string;
  };
  
  // App state
  version: string;
  platform: string;
  colorScheme: 'light' | 'dark';
  themeParams: ThemeParams;
  isExpanded: boolean;
  viewportHeight: number;
  viewportStableHeight: number;
  headerColor: string;
  backgroundColor: string;
  isClosingConfirmationEnabled: boolean;
  
  // Components
  BackButton: BackButton;
  MainButton: MainButton;
  HapticFeedback: HapticFeedback;
  
  // Methods
  isVersionAtLeast: (version: string) => boolean;
  setHeaderColor: (color: string) => void;
  setBackgroundColor: (color: string) => void;
  enableClosingConfirmation: () => void;
  disableClosingConfirmation: () => void;
  onEvent: (eventType: string, eventHandler: () => void) => void;
  offEvent: (eventType: string, eventHandler: () => void) => void;
  sendData: (data: Record<string, unknown> | string) => void;
  switchInlineQuery: (query: string, choose_chat_types?: string[]) => void;
  openLink: (url: string, options?: { try_instant_view?: boolean }) => void;
  openTelegramLink: (url: string) => void;
  openInvoice: (url: string, callback?: (status: 'paid' | 'cancelled' | 'failed' | 'pending') => void) => void;
  showPopup: (params: PopupParams, callback?: (button_id: string | null) => void) => void;
  showAlert: (message: string, callback?: () => void) => void;
  showConfirm: (message: string, callback?: (confirmed: boolean) => void) => void;
  showScanQrPopup: (params: { text?: string }, callback: (text: string) => void) => void;
  closeScanQrPopup: () => void;
  readTextFromClipboard: (callback: (text: string | null) => void) => void;
  requestWriteAccess: (callback?: (accessGranted: boolean) => void) => void;
  requestContact: (callback: (phoneNumber: string) => void) => void;
  ready: () => void;
  expand: () => void;
  close: () => void;
}

declare global {
  interface Window {
    Telegram?: {
      WebApp: TelegramWebApp;
    };
    WebApp?: TelegramWebApp;
  }
}

export const useTelegram = () => {
  const [isReady, setIsReady] = useState(false);
  const [tg, setTg] = useState<TelegramWebApp | null>(null);
  const [user, setUser] = useState<TelegramWebApp['initDataUnsafe']['user'] | null>(null);
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [viewportHeight, setViewportHeight] = useState(0);
  const [isExpanded, setIsExpanded] = useState(false);

  // Initialize Telegram WebApp
  useEffect(() => {
    const tgWebApp = window.Telegram?.WebApp || window.WebApp;
    
    if (!tgWebApp) {
      console.warn('Telegram WebApp not found. Running in browser mode.');
      setIsReady(true);
      return;
    }

    setTg(tgWebApp);
    setUser(tgWebApp.initDataUnsafe?.user || null);
    setTheme(tgWebApp.colorScheme);
    setViewportHeight(tgWebApp.viewportHeight);
    setIsExpanded(tgWebApp.isExpanded);

    // Set up event listeners
    const handleViewportChanged = () => {
      setViewportHeight(tgWebApp.viewportHeight);
      setIsExpanded(tgWebApp.isExpanded);
      
      // Update CSS custom properties
      document.documentElement.style.setProperty('--tg-viewport-height', `${tgWebApp.viewportHeight}px`);
      document.documentElement.style.setProperty('--tg-viewport-stable-height', `${tgWebApp.viewportStableHeight}px`);
    };

    const handleThemeChanged = () => {
      setTheme(tgWebApp.colorScheme);
      // Apply theme colors to CSS variables
      Object.entries(tgWebApp.themeParams).forEach(([key, value]) => {
        if (value) {
          document.documentElement.style.setProperty(`--tg-theme-${key}`, value);
        }
      });
    };

    // Set up event listeners
    tgWebApp.onEvent('viewportChanged', handleViewportChanged);
    tgWebApp.onEvent('themeChanged', handleThemeChanged);

    // Initial setup
    handleViewportChanged();
    handleThemeChanged();
    
    // Expand the web app and notify Telegram that the app is ready
    tgWebApp.expand();
    tgWebApp.ready();
    setIsReady(true);

    // Cleanup
    return () => {
      tgWebApp.offEvent('viewportChanged', handleViewportChanged);
      tgWebApp.offEvent('themeChanged', handleThemeChanged);
    };
  }, []);

  // Haptic feedback utilities
  const hapticFeedback = useMemo(() => ({
    impact: (style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft' = 'medium') => {
      tg?.HapticFeedback.impactOccurred(style);
    },
    notification: (type: 'error' | 'success' | 'warning' = 'success') => {
      tg?.HapticFeedback.notificationOccurred(type);
    },
    selection: () => {
      tg?.HapticFeedback.selectionChanged();
    }
  }), [tg]);

  // Main button utilities
  const mainButton = useMemo(() => ({
    show: () => tg?.MainButton.show(),
    hide: () => tg?.MainButton.hide(),
    enable: () => tg?.MainButton.enable(),
    disable: () => tg?.MainButton.disable(),
    showProgress: (leaveActive?: boolean) => tg?.MainButton.showProgress(leaveActive),
    hideProgress: () => tg?.MainButton.hideProgress(),
    setText: (text: string) => tg?.MainButton.setText(text),
    setParams: (params: {
      color?: string;
      text_color?: string;
      is_active?: boolean;
      is_visible?: boolean;
      text?: string;
    }) => tg?.MainButton.setParams(params),
    onClick: (callback: () => void) => {
      if (tg?.MainButton) {
        tg.MainButton.onClick(callback);
        return () => tg.MainButton.offClick(callback);
      }
      return () => {}; // No-op if MainButton is not available
    },
  }), [tg]);

  // Back button utilities
  const backButton = useMemo(() => ({
    show: () => tg?.BackButton.show(),
    hide: () => tg?.BackButton.hide(),
    onClick: (callback: () => void) => {
      if (tg?.BackButton) {
        tg.BackButton.onClick(callback);
        return () => tg.BackButton.offClick(callback);
      }
      return () => {}; // No-op if BackButton is not available
    },
  }), [tg]);

  // Popup utilities
  const showPopup = useCallback((params: PopupParams): Promise<string | null> => {
    return new Promise((resolve) => {
      if (!tg?.showPopup) {
        // Fallback for browser testing
        window.alert(params.message);
        resolve(null);
        return;
      }
      tg.showPopup(params, (buttonId) => {
        resolve(buttonId);
      });
    });
  }, [tg]);

  // Alert utility
  const showAlert = useCallback((message: string): Promise<void> => {
    return new Promise((resolve) => {
      if (!tg?.showAlert) {
        // Fallback for browser testing
        window.alert(message);
        resolve();
        return;
      }
      tg.showAlert(message, resolve);
    });
  }, [tg]);

  // Confirm dialog utility
  const showConfirm = useCallback((message: string): Promise<boolean> => {
    return new Promise((resolve) => {
      if (!tg?.showConfirm) {
        // Fallback for browser testing
        const result = window.confirm(message);
        resolve(result);
        return;
      }
      tg.showConfirm(message, resolve);
    });
  }, [tg]);

  // Close the app
  const closeApp = useCallback(() => {
    tg?.close();
  }, [tg]);

  // Check if the current version supports a feature
  const isVersionAtLeast = useCallback((version: string): boolean => {
    return tg?.isVersionAtLeast?.(version) || false;
  }, [tg]);

  return {
    // Core objects
    tg,
    user,
    isReady,
    theme,
    viewportHeight,
    isExpanded,
    
    // Main utilities
    hapticFeedback,
    mainButton,
    backButton,
    
    // Methods
    showPopup,
    showAlert,
    showConfirm,
    closeApp,
    isVersionAtLeast,
    
    // Direct access to Telegram WebApp API
    WebApp: tg,
  };
};

export type { TelegramWebApp, ThemeParams, PopupParams, PopupButton };
