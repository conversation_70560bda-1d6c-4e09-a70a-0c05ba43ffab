import React, { useState } from 'react';
import { ArrowLef<PERSON>, Minus, Plus, Trash2 } from 'lucide-react';
import { useCart } from '../../contexts/CartContext';
import { useTelegram } from '../../hooks/useTelegram';

interface CartProps {
  onBack: () => void;
}

const Cart: React.FC<CartProps> = ({ onBack }) => {
  const { state, dispatch } = useCart();
  const [discountCode, setDiscountCode] = useState('');
  const { tg } = useTelegram();

  const handleApplyDiscount = () => {
    if (discountCode.toLowerCase() === 'luxora20') {
      dispatch({ type: 'APPLY_DISCOUNT', payload: { code: discountCode, amount: state.total * 0.2 } });
    } else if (discountCode.toLowerCase() === 'welcome10') {
      dispatch({ type: 'APPLY_DISCOUNT', payload: { code: discountCode, amount: state.total * 0.1 } });
    }
  };

  const handleUpdateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      dispatch({ type: 'REMOVE_ITEM', payload: id });
    } else {
      dispatch({ type: 'UPDATE_QUANTITY', payload: { id, quantity: newQuantity } });
    }
  };

  const finalTotal = state.total - state.discountAmount;

  const handleCheckout = () => {
    if (tg) {
      tg.sendData(JSON.stringify({
        action: 'purchase',
        items: state.items,
        total: finalTotal,
        discountCode: state.discountCode
      }));
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 animate-fade-in">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl px-6 pt-12 pb-6 border-b border-gray-100/50">
        <div className="flex items-center justify-between">
          <button
            onClick={onBack}
            className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
          >
            <ArrowLeft className="w-6 h-6 text-gray-700" />
          </button>
          <h1 className="text-xl font-semibold text-gray-900 tracking-tight">Shopping Cart</h1>
          <div className="w-10"></div>
        </div>
      </div>

      <div className="px-6 py-6">
        {state.items.length === 0 ? (
          <div className="text-center py-24 animate-slide-up">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
            </div>
            <p className="text-gray-500 text-lg font-medium mb-8">Your cart is empty</p>
            <button
              onClick={onBack}
              className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-2xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              Start Shopping
            </button>
          </div>
        ) : (
          <>
            {/* Cart Items */}
            <div className="space-y-4 mb-8">
              {state.items.map((item, index) => (
                <div 
                  key={`${item.product.id}-${item.selectedColor}`} 
                  className="bg-white rounded-3xl p-6 shadow-soft animate-slide-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="flex items-center space-x-4">
                    <img
                      src={item.product.image}
                      alt={item.product.name}
                      className="w-20 h-20 object-cover rounded-2xl"
                    />
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 text-lg tracking-tight">{item.product.name}</h3>
                      {item.selectedColor && (
                        <p className="text-gray-500 text-sm font-medium">Color: {item.selectedColor}</p>
                      )}
                      <div className="flex items-center justify-between mt-4">
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() => handleUpdateQuantity(item.product.id, item.quantity - 1)}
                            className="w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-all duration-200"
                          >
                            <Minus className="w-4 h-4 text-gray-600" />
                          </button>
                          <span className="font-semibold text-gray-900 min-w-[20px] text-center">
                            {item.quantity}
                          </span>
                          <button
                            onClick={() => handleUpdateQuantity(item.product.id, item.quantity + 1)}
                            className="w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-all duration-200"
                          >
                            <Plus className="w-4 h-4 text-gray-600" />
                          </button>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className="text-xl font-bold text-gray-900 tracking-tight">
                            ${(item.product.price * item.quantity).toFixed(2)}
                          </span>
                          <button
                            onClick={() => dispatch({ type: 'REMOVE_ITEM', payload: item.product.id })}
                            className="p-2 text-red-500 hover:bg-red-50 rounded-xl transition-all duration-200"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Discount Code */}
            <div className="bg-white rounded-3xl p-6 mb-6 shadow-soft">
              <div className="flex space-x-3">
                <input
                  type="text"
                  value={discountCode}
                  onChange={(e) => setDiscountCode(e.target.value)}
                  placeholder="Enter discount code"
                  className="flex-1 px-4 py-3 bg-gray-50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-green-500/20 border border-gray-100 transition-all duration-200"
                />
                <button
                  onClick={handleApplyDiscount}
                  className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-2xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  Apply
                </button>
              </div>
            </div>

            {/* Order Summary */}
            <div className="bg-white rounded-3xl p-6 mb-6 shadow-soft">
              <div className="space-y-3">
                <div className="flex justify-between text-gray-600 font-medium">
                  <span>Subtotal</span>
                  <span>${state.total.toFixed(2)}</span>
                </div>
                {state.discountAmount > 0 && (
                  <div className="flex justify-between text-green-600 font-medium">
                    <span>Discount ({state.discountCode})</span>
                    <span>-${state.discountAmount.toFixed(2)}</span>
                  </div>
                )}
                <div className="border-t border-gray-100 pt-3">
                  <div className="flex justify-between text-xl font-bold text-gray-900 tracking-tight">
                    <span>Total</span>
                    <span>${finalTotal.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Checkout Button */}
            <button
              onClick={handleCheckout}
              className="w-full bg-green-600 hover:bg-green-700 text-white py-4 rounded-2xl text-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
            >
              Checkout
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default Cart;