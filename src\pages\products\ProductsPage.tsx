import React, { useState } from 'react';
import { Search, Heart, Filter, ShoppingBag, ArrowLeft, Star } from 'lucide-react';
import { Product } from '../../types/product';
import { products } from '../../data/products';
import { useTelegram } from '../../hooks/useTelegram';
import { useProductSearch } from '../../hooks/useProductSearch';
import { useCart } from '../../contexts/CartContext';
import { useNavigate } from 'react-router-dom';

const ProductsPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [showFilters, setShowFilters] = useState(false);
  const [priceRange, setPriceRange] = useState([0, 1000]);
  const navigate = useNavigate();
  const { state } = useCart();
  const { hapticFeedback } = useTelegram();
  const { addToRecentSearches } = useProductSearch();

  // Get all products
  const allProducts = products;
  const totalProducts = allProducts.length;

  const handleBack = () => {
    hapticFeedback.light();
    navigate(-1);
  };

  const handleProductSelect = (product: Product) => {
    hapticFeedback.medium();
    navigate(`/products/${product.id}`);
  };

  const handleCartClick = () => {
    hapticFeedback.medium();
    navigate('/cart');
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      addToRecentSearches(query);
    }
  };

  const filteredProducts = allProducts
    .filter(product => {
      // Search filter
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.category.toLowerCase().includes(searchQuery.toLowerCase());

      // Category filter
      const matchesCategory = selectedCategory === 'all' ||
        product.category.toLowerCase() === selectedCategory.toLowerCase();

      // Price filter
      const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];

      return matchesSearch && matchesCategory && matchesPrice;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'name':
          return a.name.localeCompare(b.name);
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        default:
          return 0;
      }
    });

  return (
    <div className="min-h-screen bg-gray-50 relative">
      {/* Main Content */}
      <div className="relative z-10 max-w-sm mx-auto bg-white min-h-screen pb-24">
        {/* Modern Header */}
        <div className="px-4 pt-12 pb-6 bg-white border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handleBack}
              className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-700" />
            </button>
            <h1 className="font-display text-xl text-primary">Products</h1>
            <button
              onClick={() => setShowFilters(true)}
              className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              <Filter className="w-5 h-5 text-gray-700" />
            </button>
          </div>

          <div className="text-center mb-4">
            <span className="font-display text-4xl text-primary leading-none">{filteredProducts.length}</span>
            <p className="font-caption text-secondary mt-1 text-sm">Products Available</p>
          </div>

          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-12 pr-4 py-3 bg-gray-100 border-0 rounded-xl font-text text-primary placeholder-gray-500 focus:outline-none focus:bg-white focus:ring-2 focus:ring-blue-500 transition-all"
            />
          </div>
        </div>

        {/* Category Tabs */}
        <div className="px-4 py-4 bg-white border-b border-gray-200">
          <div className="flex space-x-4 overflow-x-auto">
            {['all', 'skincare', 'makeup', 'fragrance'].map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-full text-sm font-text whitespace-nowrap transition-colors ${
                  selectedCategory === category
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Sort Options */}
        <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <span className="font-text text-secondary text-sm">
              {filteredProducts.length} products found
            </span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="bg-white border border-gray-300 rounded-lg px-3 py-1 text-sm font-text text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="name">Sort by Name</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="rating">Highest Rated</option>
            </select>
          </div>
        </div>

        {/* Modern Products Grid */}
        <div className="px-4 py-6">
          <div className="space-y-4">
            {filteredProducts.map((product, index) => (
              <ModernProductCard
                key={product.id}
                product={product}
                onSelect={handleProductSelect}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Filter Modal */}
      {showFilters && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-end">
          <div className="bg-white w-full max-w-sm mx-auto rounded-t-2xl p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="font-display text-xl text-primary">Filters</h3>
              <button
                onClick={() => setShowFilters(false)}
                className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center"
              >
                ×
              </button>
            </div>

            <div className="space-y-6">
              {/* Price Range */}
              <div>
                <label className="font-text font-medium text-primary mb-3 block">
                  Price Range: ${priceRange[0]} - ${priceRange[1]}
                </label>
                <div className="flex space-x-4">
                  <input
                    type="range"
                    min="0"
                    max="1000"
                    value={priceRange[0]}
                    onChange={(e) => setPriceRange([parseInt(e.target.value), priceRange[1]])}
                    className="flex-1"
                  />
                  <input
                    type="range"
                    min="0"
                    max="1000"
                    value={priceRange[1]}
                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                    className="flex-1"
                  />
                </div>
              </div>

              {/* Apply Filters */}
              <button
                onClick={() => setShowFilters(false)}
                className="btn-primary w-full"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modern Bottom Navigation - NEVER scrolls */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200">
        <div className="max-w-sm mx-auto">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            </div>
            <h2 className="font-display text-2xl text-primary">Store</h2>
            <button
              onClick={handleCartClick}
              className="relative p-2 bg-blue-500 hover:bg-blue-600 rounded-full transition-colors"
            >
              <ShoppingBag className="w-5 h-5 text-white" />
              {state.items.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                  {state.items.length}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Modern Product Card Component
const ModernProductCard: React.FC<{
  product: Product;
  onSelect: (product: Product) => void;
}> = ({ product, onSelect }) => {
  const { dispatch } = useCart();
  const { hapticFeedback, showAlert } = useTelegram();
  const [isWishlisted, setIsWishlisted] = useState(false);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    hapticFeedback.medium();
    dispatch({
      type: 'ADD_ITEM',
      payload: { product }
    });
    showAlert('Added to cart!');
  };

  const handleWishlist = (e: React.MouseEvent) => {
    e.stopPropagation();
    hapticFeedback.light();
    setIsWishlisted(!isWishlisted);
    showAlert(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist');
  };

  return (
    <div className="modern-card p-4">
      <div className="flex space-x-4">
        {/* Product Image */}
        <div
          className="w-20 h-20 flex-shrink-0 cursor-pointer group overflow-hidden rounded-lg relative"
          onClick={() => onSelect(product)}
        >
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <button
            onClick={handleWishlist}
            className="absolute top-1 right-1 w-6 h-6 bg-white bg-opacity-90 rounded-full flex items-center justify-center"
          >
            <Heart className={`w-3 h-3 ${isWishlisted ? 'text-red-500 fill-current' : 'text-gray-400'}`} />
          </button>
        </div>

        {/* Product Info */}
        <div className="flex-1 min-w-0">
          <div
            className="cursor-pointer mb-2"
            onClick={() => onSelect(product)}
          >
            <div className="flex items-center justify-between mb-1">
              <h3 className="font-text font-semibold text-primary text-lg truncate">{product.name}</h3>
              {product.discount && (
                <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full font-medium">
                  -{Math.round(((product.originalPrice! - product.price) / product.originalPrice!) * 100)}%
                </span>
              )}
            </div>

            {/* Rating */}
            <div className="flex items-center space-x-1 mb-1">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-3 h-3 ${
                    i < Math.floor(product.rating || 4.5)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
              <span className="font-caption text-secondary text-xs ml-1">
                ({product.rating || 4.5}) • {Math.floor(Math.random() * 100) + 10} reviews
              </span>
            </div>

            <p className="font-caption text-secondary text-sm leading-relaxed">
              Premium quality product with excellent features
            </p>
          </div>

          <div className="flex items-center justify-between">
            <div>
              {product.discount && (
                <span className="font-caption text-secondary text-sm line-through block">
                  ${product.originalPrice?.toFixed(2)}
                </span>
              )}
              <span className="font-text font-semibold text-primary text-xl">
                ${product.price.toFixed(2)}
              </span>
            </div>

            <button
              onClick={handleAddToCart}
              className="btn-primary px-4 py-2 text-sm"
            >
              Add to Cart
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
export default ProductsPage;
