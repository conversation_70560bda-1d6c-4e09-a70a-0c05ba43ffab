import React, { useState, useEffect, useMemo } from 'react';
import { Search, Filter, Grid, List, ChevronDown, X } from 'lucide-react';
import { Product, ProductFilter, ProductSortOption } from '../../types/product';
import { products } from '../../data/products';
import { useTelegram } from '../../hooks/useTelegram';
import { useProductSearch } from '../../hooks/useProductSearch';
import ProductCard from '../../components/Product/ProductCard';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { SearchBar } from '../../components/common/SearchBar';
import { FilterPanel } from '../../components/common/FilterPanel';

const ITEMS_PER_PAGE = 12;

const ProductsPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { hapticFeedback, mainButton } = useTelegram();
  const {
    query,
    results,
    filters,
    sortBy,
    totalResults,
    recentSearches,
    search,
    addFilter,
    removeFilter,
    clearFilters,
    setSortBy,
    addToRecentSearches,
    getSuggestions,
  } = useProductSearch();

  // Get available categories and brands for filters
  const availableCategories = useMemo(() => {
    const categoryCount: Record<string, number> = {};
    products.forEach(product => {
      categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;
    });
    return Object.entries(categoryCount).map(([category, count]) => ({
      category: category as any,
      count,
    }));
  }, []);

  const availableBrands = useMemo(() => {
    const brandCount: Record<string, number> = {};
    products.forEach(product => {
      if (product.brand) {
        brandCount[product.brand] = (brandCount[product.brand] || 0) + 1;
      }
    });
    return Object.entries(brandCount).map(([brand, count]) => ({
      brand,
      count,
    }));
  }, []);

  const priceRange = useMemo(() => {
    const prices = products.map(p => p.price);
    return {
      min: Math.min(...prices),
      max: Math.max(...prices),
    };
  }, []);

  // Paginate products
  const paginatedProducts = useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    return results.slice(startIndex, startIndex + ITEMS_PER_PAGE);
  }, [results, currentPage]);

  const totalPages = Math.ceil(results.length / ITEMS_PER_PAGE);
  const hasMore = currentPage < totalPages;

  // Handle search
  const handleSearch = (searchQuery: string) => {
    search(searchQuery);
    addToRecentSearches(searchQuery);
    setCurrentPage(1);
    hapticFeedback.light();
  };

  // Handle sort change
  const handleSortChange = (sort: ProductSortOption) => {
    setSortBy(sort);
    setCurrentPage(1);
    hapticFeedback.light();
  };

  // Handle filter change
  const handleFilterChange = (newFilters: ProductFilter) => {
    // Update filters using the search hook
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined) {
        addFilter({ [key]: value });
      } else {
        removeFilter(key as any);
      }
    });
    setCurrentPage(1);
    hapticFeedback.light();
  };

  // Handle product selection
  const handleProductSelect = (product: Product) => {
    hapticFeedback.medium();
    // Navigate to product detail page
    window.location.href = `/products/${product.id}`;
  };

  // Load more products
  const loadMore = () => {
    if (hasMore && !isLoading) {
      setIsLoading(true);
      hapticFeedback.light();
      
      // Simulate loading delay
      setTimeout(() => {
        setCurrentPage(prev => prev + 1);
        setIsLoading(false);
      }, 500);
    }
  };

  // Configure main button for load more
  useEffect(() => {
    if (hasMore) {
      mainButton.setText('Load More Products');
      mainButton.show();
      mainButton.onClick(loadMore);
    } else {
      mainButton.hide();
    }

    return () => {
      mainButton.offClick(loadMore);
    };
  }, [hasMore, mainButton]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="px-4 py-3">
          <h1 className="text-xl font-bold text-gray-900 mb-3">Products</h1>
          
          {/* Search Bar */}
          <SearchBar
            value={query}
            onChange={search}
            onSearch={handleSearch}
            suggestions={getSuggestions(query)}
            recentSearches={recentSearches}
            placeholder="Search products..."
            className="mb-3"
          />

          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => {
                  setShowFilters(!showFilters);
                  hapticFeedback.light();
                }}
                className="flex items-center space-x-1 px-3 py-2 bg-gray-100 rounded-lg"
              >
                <Filter className="w-4 h-4" />
                <span className="text-sm">Filters</span>
              </button>

              <select
                value={sortBy}
                onChange={(e) => handleSortChange(e.target.value as ProductSortOption)}
                className="px-3 py-2 bg-gray-100 rounded-lg text-sm"
              >
                <option value="newest">Newest</option>
                <option value="oldest">Oldest</option>
                <option value="price-low-high">Price: Low to High</option>
                <option value="price-high-low">Price: High to Low</option>
                <option value="rating-high-low">Rating: High to Low</option>
                <option value="name-a-z">Name: A to Z</option>
                <option value="popularity">Most Popular</option>
              </select>
            </div>

            <div className="flex items-center space-x-1">
              <button
                onClick={() => {
                  setViewMode('grid');
                  hapticFeedback.light();
                }}
                className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => {
                  setViewMode('list');
                  hapticFeedback.light();
                }}
                className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results Info */}
      <div className="px-4 py-3 bg-white border-b border-gray-100">
        <p className="text-sm text-gray-600">
          Showing {paginatedProducts.length} of {totalResults} products
        </p>
      </div>

      {/* Products Grid */}
      <div className="p-4">
        {paginatedProducts.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-2">
              <Search className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-1">No products found</h3>
            <p className="text-gray-500">Try adjusting your search or filters</p>
          </div>
        ) : (
          <div className={`grid gap-4 ${viewMode === 'grid' ? 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4' : 'grid-cols-1'}`}>
            {paginatedProducts.map((product, index) => (
              <div
                key={product.id}
                className="animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <ProductCard
                  product={product}
                  onSelect={handleProductSelect}
                />
              </div>
            ))}
          </div>
        )}

        {/* Loading Spinner */}
        {isLoading && (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="md" />
          </div>
        )}
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <FilterPanel
          filters={filters}
          onFiltersChange={handleFilterChange}
          onClose={() => setShowFilters(false)}
          availableCategories={availableCategories}
          availableBrands={availableBrands}
          priceRange={priceRange}
        />
      )}
    </div>
  );
};

export default ProductsPage;
