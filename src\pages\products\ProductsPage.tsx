import React, { useState } from 'react';
import { Search, Heart, Filter, ShoppingBag, ArrowLeft } from 'lucide-react';
import { Product } from '../../types/product';
import { products } from '../../data/products';
import { useTelegram } from '../../hooks/useTelegram';
import { useProductSearch } from '../../hooks/useProductSearch';
import { useCart } from '../../contexts/CartContext';
import { useNavigate } from 'react-router-dom';

const ProductsPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const navigate = useNavigate();
  const { state } = useCart();
  const { hapticFeedback } = useTelegram();
  const { addToRecentSearches } = useProductSearch();

  // Get all products
  const allProducts = products;
  const totalProducts = allProducts.length;

  const handleBack = () => {
    hapticFeedback.light();
    navigate(-1);
  };

  const handleProductSelect = (product: Product) => {
    hapticFeedback.medium();
    navigate(`/products/${product.id}`);
  };

  const handleCartClick = () => {
    hapticFeedback.medium();
    navigate('/cart');
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      addToRecentSearches(query);
    }
  };

  const filteredProducts = allProducts.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-green-100 to-green-200 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200 rounded-full opacity-30"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-green-300 rounded-full opacity-20"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-sm mx-auto bg-white/90 backdrop-blur-sm min-h-screen">
        {/* Header with count and filter */}
        <div className="flex items-center justify-between p-6 pt-12">
          <button
            onClick={handleBack}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-6 h-6 text-gray-600" />
          </button>
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900">{filteredProducts.length}</h1>
            <p className="text-lg text-gray-600 font-medium">Products</p>
          </div>
          <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <Filter className="w-6 h-6 text-gray-600" />
          </button>
        </div>

        {/* Search Bar */}
        <div className="px-6 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search your product"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-gray-100 rounded-xl border-0 focus:ring-2 focus:ring-green-500 focus:bg-white transition-all"
            />
          </div>
        </div>

        {/* Category Pills */}
        <div className="px-6 mb-6">
          <div className="flex space-x-4">
            <div className="flex items-center space-x-2 bg-pink-100 px-4 py-2 rounded-full">
              <span className="text-sm font-medium text-gray-700">Hyper</span>
              <Heart className="w-4 h-4 text-pink-500 fill-current" />
            </div>
            <div className="flex items-center space-x-2 bg-gray-100 px-4 py-2 rounded-full">
              <span className="text-sm font-medium text-gray-700">Frame v1</span>
              <div className="w-4 h-4 border border-gray-400 rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="px-6 space-y-6 pb-24">
          {filteredProducts.map((product, index) => (
            <div key={product.id} className={`${index % 2 === 0 ? '' : 'ml-8'}`}>
              <ProductCard product={product} onSelect={handleProductSelect} />
            </div>
          ))}
        </div>

        {/* Bottom Navigation */}
        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm bg-white/95 backdrop-blur-sm border-t border-gray-200">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-800 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            </div>
            <h2 className="text-xl font-bold text-gray-900">Luxora</h2>
            <button
              onClick={handleCartClick}
              className="relative p-2 bg-pink-500 rounded-lg"
            >
              <ShoppingBag className="w-5 h-5 text-white" />
              {state.items.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center text-[10px]">
                  {state.items.length}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Product Card Component matching the design
const ProductCard: React.FC<{ product: Product; onSelect: (product: Product) => void }> = ({
  product,
  onSelect
}) => {
  return (
    <div
      className="cursor-pointer group"
      onClick={() => onSelect(product)}
    >
      {/* Product Image */}
      <div className="relative mb-3">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-32 object-cover rounded-2xl"
        />
        <button className="absolute top-3 right-3 p-1.5 bg-white/80 backdrop-blur-sm rounded-full">
          <Heart className="w-4 h-4 text-gray-600" />
        </button>
      </div>

      {/* Product Info */}
      <div className="space-y-1">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-gray-900">{product.name}</h3>
          <div className="w-4 h-4 border border-gray-400 rounded-full"></div>
        </div>
        <div className="flex items-center space-x-2">
          {product.discount && (
            <span className="text-sm text-gray-400 line-through">
              ${product.originalPrice?.toFixed(2)}
            </span>
          )}
          <span className="text-lg font-bold text-gray-900">
            ${product.price.toFixed(2)}
          </span>
        </div>
      </div>
    </div>
  );
};
export default ProductsPage;
