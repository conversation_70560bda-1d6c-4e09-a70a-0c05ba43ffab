import React, { useState } from 'react';
import { Search, Heart, Filter, ShoppingBag, ArrowLeft } from 'lucide-react';
import { Product } from '../../types/product';
import { products } from '../../data/products';
import { useTelegram } from '../../hooks/useTelegram';
import { useProductSearch } from '../../hooks/useProductSearch';
import { useCart } from '../../contexts/CartContext';
import { useNavigate } from 'react-router-dom';

const ProductsPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const navigate = useNavigate();
  const { state } = useCart();
  const { hapticFeedback } = useTelegram();
  const { addToRecentSearches } = useProductSearch();

  // Get all products
  const allProducts = products;
  const totalProducts = allProducts.length;

  const handleBack = () => {
    hapticFeedback.light();
    navigate(-1);
  };

  const handleProductSelect = (product: Product) => {
    hapticFeedback.medium();
    navigate(`/products/${product.id}`);
  };

  const handleCartClick = () => {
    hapticFeedback.medium();
    navigate('/cart');
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      addToRecentSearches(query);
    }
  };

  const filteredProducts = allProducts.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="app-container bg-gradient-to-br from-green-50 via-green-100 to-green-200 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200 rounded-full opacity-30"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-green-300 rounded-full opacity-20"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 bg-white/95 backdrop-blur-sm min-h-screen mobile-scroll">
        {/* Header with count and filter */}
        <div className="flex items-center justify-between p-6 pt-16">
          <button
            onClick={handleBack}
            className="touch-target p-3 hover:bg-gray-100 rounded-xl transition-colors"
          >
            <ArrowLeft className="w-6 h-6 text-gray-600" />
          </button>
          <div className="text-center">
            <h1 className="text-5xl font-bold text-gray-900 leading-none">{filteredProducts.length}</h1>
            <p className="text-xl text-gray-600 font-medium mt-1">Products</p>
          </div>
          <button className="touch-target p-3 hover:bg-gray-100 rounded-xl transition-colors">
            <Filter className="w-6 h-6 text-gray-600" />
          </button>
        </div>

        {/* Search Bar */}
        <div className="px-6 mb-8">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search your product"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-12 pr-4 py-4 bg-gray-50 rounded-2xl border-0 focus:ring-2 focus:ring-green-500 focus:bg-white transition-all text-base"
            />
          </div>
        </div>

        {/* Category Pills */}
        <div className="px-6 mb-8">
          <div className="flex space-x-3">
            <button className="flex items-center space-x-2 bg-pink-50 border border-pink-200 px-5 py-3 rounded-full touch-target">
              <span className="text-sm font-semibold text-pink-700">Hyper</span>
              <Heart className="w-4 h-4 text-pink-500 fill-current" />
            </button>
            <button className="flex items-center space-x-2 bg-gray-50 border border-gray-200 px-5 py-3 rounded-full touch-target">
              <span className="text-sm font-semibold text-gray-700">Frame v1</span>
              <div className="w-4 h-4 border-2 border-gray-400 rounded-full"></div>
            </button>
          </div>
        </div>

        {/* Products Grid */}
        <div className="px-6 space-y-6 pb-24">
          {filteredProducts.map((product, index) => (
            <div key={product.id} className={`${index % 2 === 0 ? '' : 'ml-8'}`}>
              <ProductCard product={product} onSelect={handleProductSelect} />
            </div>
          ))}
        </div>
      </div>

      {/* Fixed Bottom Navigation - NEVER scrolls */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white/98 backdrop-blur-xl border-t border-gray-100 safe-area-inset-bottom">
        <div className="app-container">
          <div className="flex items-center justify-between px-6 py-5">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-900 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 tracking-tight">Luxora</h2>
            <button
              onClick={handleCartClick}
              className="relative touch-target p-3 bg-pink-500 hover:bg-pink-600 active:bg-pink-700 rounded-2xl transition-colors shadow-lg"
            >
              <ShoppingBag className="w-6 h-6 text-white" />
              {state.items.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                  {state.items.length}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Product Card Component matching the design
const ProductCard: React.FC<{ product: Product; onSelect: (product: Product) => void }> = ({
  product,
  onSelect
}) => {
  const { dispatch } = useCart();
  const { hapticFeedback, showAlert } = useTelegram();

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    hapticFeedback.medium();
    dispatch({
      type: 'ADD_ITEM',
      payload: { product }
    });
    showAlert('Added to cart!');
  };

  return (
    <div className="bg-white rounded-3xl p-5 shadow-soft border border-gray-100/50">
      {/* Product Name and Heart */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-gray-900 text-lg tracking-tight">{product.name}</h3>
        <button
          onClick={(e) => {
            e.stopPropagation();
            hapticFeedback.light();
          }}
          className="touch-target p-2 hover:bg-gray-50 rounded-xl transition-colors"
        >
          <Heart className="w-5 h-5 text-gray-400" />
        </button>
      </div>

      {/* Product Image */}
      <div
        className="relative mb-4 cursor-pointer group"
        onClick={() => onSelect(product)}
      >
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-48 object-cover rounded-2xl group-hover:scale-105 transition-transform duration-300"
        />
      </div>

      {/* Price and Add to Cart */}
      <div className="space-y-4">
        <div
          className="cursor-pointer"
          onClick={() => onSelect(product)}
        >
          <div className="flex items-center space-x-2 mb-1">
            {product.discount && (
              <span className="text-sm text-gray-400 line-through">
                ${product.originalPrice?.toFixed(2)}
              </span>
            )}
            <span className="text-2xl font-bold text-gray-900">
              ${product.price.toFixed(2)}
            </span>
          </div>
        </div>

        {/* Add to Cart Button */}
        <button
          onClick={handleAddToCart}
          className="w-full bg-green-600 hover:bg-green-700 active:bg-green-800 text-white py-3 rounded-2xl font-semibold transition-colors touch-target"
        >
          Add to Cart
        </button>
      </div>
    </div>
  );
};
export default ProductsPage;
