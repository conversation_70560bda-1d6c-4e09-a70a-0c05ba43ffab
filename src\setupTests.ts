// Import jest-dom for custom matchers
import '@testing-library/jest-dom';

// Mock the global Telegram WebApp object
const mockTelegramWebApp = {
  WebApp: {
    initDataUnsafe: {
      user: {
        id: 123456789,
        first_name: 'Test',
        last_name: 'User',
        username: 'testuser',
        language_code: 'en',
        is_premium: true,
        photo_url: 'https://example.com/photo.jpg'
      }
    },
    colorScheme: 'light',
    viewportHeight: 600,
    isExpanded: true,
    showAlert: jest.fn(),
    showConfirm: jest.fn((message, callback) => callback(true)),
    HapticFeedback: {
      impactOccurred: jest.fn(),
      notificationOccurred: jest.fn(),
      selectionChanged: jest.fn()
    },
    onEvent: jest.fn(),
    offEvent: jest.fn(),
    // Add other Telegram WebApp methods as needed
  }
};

// Set up the global Telegram object with proper type assertion
// This is safe because we're in a test environment
Object.defineProperty(window, 'Telegram', {
  value: mockTelegramWebApp,
  writable: true,
  configurable: true
});
