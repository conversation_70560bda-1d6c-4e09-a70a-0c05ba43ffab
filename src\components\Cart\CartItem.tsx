import React from 'react';
import { Minus, Plus, Edit2, Clock } from 'lucide-react';
import { CartItem as CartItemType } from '../../types/product';
import { useCart } from '../../contexts/CartContext';

interface CartItemProps {
  item: CartItemType;
}

const CartItem: React.FC<CartItemProps> = ({ item }) => {
  const { dispatch } = useCart();

  const handleUpdateQuantity = (newQuantity: number) => {
    if (newQuantity <= 0) {
      dispatch({ type: 'REMOVE_ITEM', payload: item.product.id });
    } else {
      dispatch({ type: 'UPDATE_QUANTITY', payload: { id: item.product.id, quantity: newQuantity } });
    }
  };

  return (
    <div className="bg-white rounded-2xl p-4 shadow-sm mb-3">
      <div className="flex items-center space-x-4">
        <img
          src={item.product.image}
          alt={item.product.name}
          className="w-16 h-16 object-cover rounded-xl"
        />
        <div className="flex-1">
          <h3 className="font-medium text-gray-900">{item.product.name}</h3>
          <div className="flex items-center mt-1 text-sm text-gray-500">
            <Clock className="w-4 h-4 mr-1" />
            <span>02:01-07:01</span>
          </div>
        </div>
        <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
          <Edit2 className="w-4 h-4 text-gray-500" />
        </button>
      </div>
      <div className="flex items-center justify-between mt-4">
        <div className="flex items-center space-x-3">
          <button
            onClick={() => handleUpdateQuantity(item.quantity - 1)}
            className="w-8 h-8 flex items-center justify-center bg-gray-100 rounded-full hover:bg-gray-200 transition-colors"
          >
            <Minus className="w-4 h-4 text-gray-600" />
          </button>
          <span className="font-medium text-gray-900 min-w-[20px] text-center">{item.quantity}</span>
          <button
            onClick={() => handleUpdateQuantity(item.quantity + 1)}
            className="w-8 h-8 flex items-center justify-center bg-gray-100 rounded-full hover:bg-gray-200 transition-colors"
          >
            <Plus className="w-4 h-4 text-gray-600" />
          </button>
        </div>
        <div className="text-lg font-bold text-gray-900">
          {(item.product.price * item.quantity).toFixed(2)} €
        </div>
      </div>
    </div>
  );
};

export default CartItem;