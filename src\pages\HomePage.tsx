import React, { useState, useEffect } from 'react';
import { Search, ShoppingCart, Heart, Menu, Star, ArrowRight } from 'lucide-react';
import { Product } from '../types/product';
import { products, deals } from '../data/products';
import { useTelegram } from '../hooks/useTelegram';
import { useCart } from '../contexts/CartContext';
import { useProductSearch } from '../hooks/useProductSearch';
import ProductCard from '../components/Product/ProductCard';
import { SearchBar } from '../components/common/SearchBar';

const HomePage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showMenu, setShowMenu] = useState(false);
  const { state } = useCart();
  const { hapticFeedback, showAlert } = useTelegram();
  const { recentSearches, getSuggestions, addToRecentSearches } = useProductSearch();
  
  const featuredProducts = products.filter(p => p.isFeatured).slice(0, 4);
  const newArrivals = products.filter(p => p.isNew).slice(0, 4);
  const onSaleProducts = products.filter(p => p.discount && p.discount > 0).slice(0, 4);

  const handleMenuToggle = () => {
    hapticFeedback.light();
    setShowMenu(!showMenu);
  };

  const handleCartClick = () => {
    hapticFeedback.medium();
    window.location.href = '/cart';
  };

  const handleWishlistClick = () => {
    hapticFeedback.medium();
    window.location.href = '/wishlist';
  };

  const handleProductSelect = (product: Product) => {
    hapticFeedback.medium();
    window.location.href = `/products/${product.id}`;
  };

  const handleViewAllProducts = () => {
    hapticFeedback.medium();
    window.location.href = '/products';
  };

  const handleSearch = (query: string) => {
    if (query.trim()) {
      hapticFeedback.medium();
      addToRecentSearches(query);
      window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-24">
      {/* Header */}
      <div className="bg-white border-b border-gray-100 px-4 py-3 sticky top-0 z-10">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h1 className="text-xl font-bold text-gray-900">Souq</h1>
            <p className="text-sm text-gray-500">Premium Beauty & Eyewear</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleWishlistClick}
              className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
            >
              <Heart className="w-6 h-6 text-gray-600" />
            </button>
            <button
              onClick={handleCartClick}
              className="relative p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
            >
              <ShoppingCart className="w-6 h-6 text-gray-600" />
              {state.items.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {state.items.length}
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Search Bar */}
        <SearchBar
          value={searchQuery}
          onChange={setSearchQuery}
          onSearch={handleSearch}
          suggestions={getSuggestions(searchQuery)}
          recentSearches={recentSearches}
          placeholder="Search products, brands..."
        />
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 m-4 rounded-2xl">
        <h2 className="text-2xl font-bold mb-2">Welcome to Souq</h2>
        <p className="text-green-100 mb-4">Discover premium beauty products and eyewear</p>
        <button
          onClick={handleViewAllProducts}
          className="bg-white text-green-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
        >
          Shop Now
        </button>
      </div>

      {/* Categories */}
      <div className="px-4 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Categories</h3>
        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={() => window.location.href = '/products?category=sunglasses'}
            className="bg-white p-4 rounded-xl border border-gray-200 hover:border-green-300 transition-colors"
          >
            <div className="text-2xl mb-2">🕶️</div>
            <div className="font-medium text-gray-900">Sunglasses</div>
            <div className="text-sm text-gray-500">Premium eyewear</div>
          </button>
          <button
            onClick={() => window.location.href = '/products?category=skincare'}
            className="bg-white p-4 rounded-xl border border-gray-200 hover:border-green-300 transition-colors"
          >
            <div className="text-2xl mb-2">🧴</div>
            <div className="font-medium text-gray-900">Skincare</div>
            <div className="text-sm text-gray-500">Beauty essentials</div>
          </button>
        </div>
      </div>

      {/* Featured Products */}
      {featuredProducts.length > 0 && (
        <div className="px-4 mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold text-gray-900">Featured Products</h3>
            <button
              onClick={handleViewAllProducts}
              className="text-green-600 text-sm font-medium flex items-center"
            >
              View All <ArrowRight className="w-4 h-4 ml-1" />
            </button>
          </div>
          <div className="grid grid-cols-2 gap-4">
            {featuredProducts.map((product, index) => (
              <div
                key={product.id}
                className="animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <ProductCard
                  product={product}
                  onSelect={handleProductSelect}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* New Arrivals */}
      {newArrivals.length > 0 && (
        <div className="px-4 mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold text-gray-900">New Arrivals</h3>
            <button
              onClick={() => window.location.href = '/products?filter=new'}
              className="text-green-600 text-sm font-medium flex items-center"
            >
              View All <ArrowRight className="w-4 h-4 ml-1" />
            </button>
          </div>
          <div className="grid grid-cols-2 gap-4">
            {newArrivals.map((product, index) => (
              <div
                key={product.id}
                className="animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <ProductCard
                  product={product}
                  onSelect={handleProductSelect}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* On Sale */}
      {onSaleProducts.length > 0 && (
        <div className="px-4 mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold text-gray-900">On Sale</h3>
            <button
              onClick={() => window.location.href = '/products?filter=sale'}
              className="text-green-600 text-sm font-medium flex items-center"
            >
              View All <ArrowRight className="w-4 h-4 ml-1" />
            </button>
          </div>
          <div className="grid grid-cols-2 gap-4">
            {onSaleProducts.map((product, index) => (
              <div
                key={product.id}
                className="animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <ProductCard
                  product={product}
                  onSelect={handleProductSelect}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Deals */}
      {deals.length > 0 && (
        <div className="px-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Special Deals</h3>
          {deals.map((deal) => (
            <div key={deal.id} className="bg-white rounded-xl p-4 mb-4 border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">{deal.title}</h4>
                  <p className="text-sm text-gray-500">{deal.description}</p>
                </div>
                <div className="bg-red-100 text-red-600 px-2 py-1 rounded-lg text-sm font-medium">
                  {deal.discount}% OFF
                </div>
              </div>
              <div className="grid grid-cols-2 gap-3">
                {deal.products.slice(0, 2).map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    onSelect={handleProductSelect}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 px-6 py-6 bg-white/95 border-t border-gray-100/50 backdrop-blur-xl z-30">
        <div className="flex items-center justify-between">
          <button 
            onClick={handleMenuToggle}
            className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
          >
            <Menu className="w-6 h-6 text-gray-900" />
          </button>

          <button
            onClick={handleViewAllProducts}
            className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-2xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            Browse Products
          </button>

          <button
            onClick={handleCartClick}
            className="relative p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
          >
            <ShoppingCart className="w-6 h-6 text-gray-900" />
            {state.items.length > 0 && (
              <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {state.items.length}
              </span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
