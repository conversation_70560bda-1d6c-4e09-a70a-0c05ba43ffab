import React, { useState } from 'react';
import { Search, Heart, Filter, ShoppingBag, Star } from 'lucide-react';
import { Product } from '../types/product';
import { products } from '../data/products';
import { useTelegram } from '../hooks/useTelegram';
import { useCart } from '../contexts/CartContext';
import { useProductSearch } from '../hooks/useProductSearch';

const HomePage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const { state } = useCart();
  const { hapticFeedback } = useTelegram();
  const { addToRecentSearches } = useProductSearch();

  // Get new arrivals (first 4 products)
  const newArrivals = products.slice(0, 4);
  const totalNewArrivals = products.length;

  const handleMenuToggle = () => {
    hapticFeedback.light();
    setShowMenu(!showMenu);
  };

  const handleCartClick = () => {
    hapticFeedback.medium();
    window.location.href = '/cart';
  };

  const handleWishlistClick = () => {
    hapticFeedback.medium();
    window.location.href = '/wishlist';
  };

  const handleProductSelect = (product: Product) => {
    hapticFeedback.medium();
    window.location.href = `/products/${product.id}`;
  };

  const handleViewAllProducts = () => {
    hapticFeedback.medium();
    window.location.href = '/products';
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      addToRecentSearches(query);
    }
  };

  const filteredProducts = newArrivals.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-50 relative">
      {/* Main Content */}
      <div className="relative z-10 max-w-sm mx-auto bg-white min-h-screen pb-24">
        {/* Modern Header */}
        <div className="px-4 pt-12 pb-6">
          <div className="text-center mb-6">
            <h1 className="font-display text-3xl text-primary mb-2 leading-tight">
              Discover Amazing Products
            </h1>
            <p className="font-text text-secondary text-lg">
              Shop the latest trends and essentials
            </p>
          </div>

          <div className="flex items-center justify-between mb-6">
            <div>
              <span className="font-display text-5xl text-primary leading-none">{totalNewArrivals}</span>
              <p className="font-caption text-secondary mt-1 tracking-wide uppercase text-sm">New Arrivals</p>
            </div>
            <button className="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors">
              <Filter className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Modern Search Bar */}
        <div className="px-4 mb-8">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-12 pr-4 py-3 bg-gray-100 border-0 rounded-xl font-text text-primary placeholder-gray-500 focus:outline-none focus:bg-white focus:ring-2 focus:ring-blue-500 transition-all"
            />
          </div>
        </div>

        {/* Modern Categories */}
        <div className="px-4 mb-8">
          <div className="flex justify-center space-x-6">
            <button className="flex flex-col items-center space-y-2 touch-target group">
              <div className="w-14 h-14 bg-blue-100 rounded-2xl flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                <Heart className="w-6 h-6 text-blue-600" />
              </div>
              <span className="font-caption text-secondary text-xs">Skincare</span>
            </button>
            <button className="flex flex-col items-center space-y-2 touch-target group">
              <div className="w-14 h-14 bg-pink-100 rounded-2xl flex items-center justify-center group-hover:bg-pink-200 transition-colors">
                <div className="w-6 h-6 border-2 border-pink-600 rounded-full"></div>
              </div>
              <span className="font-caption text-secondary text-xs">Makeup</span>
            </button>
            <button className="flex flex-col items-center space-y-2 touch-target group">
              <div className="w-14 h-14 bg-green-100 rounded-2xl flex items-center justify-center group-hover:bg-green-200 transition-colors">
                <div className="w-6 h-6 bg-green-600 rounded-lg"></div>
              </div>
              <span className="font-caption text-secondary text-xs">Fragrance</span>
            </button>
          </div>
        </div>

        {/* Featured Products */}
        <div className="px-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="font-display text-xl text-primary">Featured Products</h2>
            <button className="font-text text-accent text-sm">See All</button>
          </div>
        </div>

        {/* Modern Products Grid */}
        <div className="px-4 pb-32">
          <div className="space-y-4">
            {filteredProducts.map((product, index) => (
              <ModernProductCard
                key={product.id}
                product={product}
                onSelect={handleProductSelect}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Modern Bottom Navigation - NEVER scrolls */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200">
        <div className="max-w-sm mx-auto">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            </div>
            <h2 className="font-display text-2xl text-primary">Store</h2>
            <button
              onClick={handleCartClick}
              className="relative p-2 bg-blue-500 hover:bg-blue-600 rounded-full transition-colors"
            >
              <ShoppingBag className="w-5 h-5 text-white" />
              {state.items.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                  {state.items.length}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Modern Product Card Component
const ModernProductCard: React.FC<{
  product: Product;
  onSelect: (product: Product) => void;
}> = ({ product, onSelect }) => {
  const { dispatch } = useCart();
  const { hapticFeedback, showAlert } = useTelegram();
  const [isWishlisted, setIsWishlisted] = useState(false);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    hapticFeedback.medium();
    dispatch({
      type: 'ADD_ITEM',
      payload: { product }
    });
    showAlert('Added to cart!');
  };

  const handleWishlist = (e: React.MouseEvent) => {
    e.stopPropagation();
    hapticFeedback.light();
    setIsWishlisted(!isWishlisted);
    showAlert(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist');
  };

  return (
    <div className="modern-card p-4">
      <div className="flex space-x-4">
        {/* Product Image */}
        <div
          className="w-20 h-20 flex-shrink-0 cursor-pointer group overflow-hidden rounded-lg relative"
          onClick={() => onSelect(product)}
        >
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <button
            onClick={handleWishlist}
            className="absolute top-1 right-1 w-6 h-6 bg-white bg-opacity-90 rounded-full flex items-center justify-center"
          >
            <Heart className={`w-3 h-3 ${isWishlisted ? 'text-red-500 fill-current' : 'text-gray-400'}`} />
          </button>
        </div>

        {/* Product Info */}
        <div className="flex-1 min-w-0">
          <div
            className="cursor-pointer mb-2"
            onClick={() => onSelect(product)}
          >
            <div className="flex items-center justify-between mb-1">
              <h3 className="font-text font-semibold text-primary text-lg truncate">{product.name}</h3>
              {product.discount && (
                <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full font-medium">
                  -{Math.round(((product.originalPrice! - product.price) / product.originalPrice!) * 100)}%
                </span>
              )}
            </div>

            {/* Rating */}
            <div className="flex items-center space-x-1 mb-1">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-3 h-3 ${
                    i < Math.floor(product.rating || 4.5)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              ))}
              <span className="font-caption text-secondary text-xs ml-1">
                ({product.rating || 4.5})
              </span>
            </div>

            <p className="font-caption text-secondary text-sm leading-relaxed">
              Premium quality product with excellent features
            </p>
          </div>

          <div className="flex items-center justify-between">
            <div>
              {product.discount && (
                <span className="font-caption text-secondary text-sm line-through block">
                  ${product.originalPrice?.toFixed(2)}
                </span>
              )}
              <span className="font-text font-semibold text-primary text-xl">
                ${product.price.toFixed(2)}
              </span>
            </div>

            <button
              onClick={handleAddToCart}
              className="btn-primary px-4 py-2 text-sm"
            >
              Add to Cart
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
