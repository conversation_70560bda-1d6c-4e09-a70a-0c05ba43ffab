import React, { useState } from 'react';
import { Search, Heart, Filter, ShoppingBag } from 'lucide-react';
import { Product } from '../types/product';
import { products } from '../data/products';
import { useTelegram } from '../hooks/useTelegram';
import { useCart } from '../contexts/CartContext';
import { useProductSearch } from '../hooks/useProductSearch';

const HomePage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const { state } = useCart();
  const { hapticFeedback } = useTelegram();
  const { addToRecentSearches } = useProductSearch();

  // Get new arrivals (first 4 products)
  const newArrivals = products.slice(0, 4);
  const totalNewArrivals = products.length;

  const handleMenuToggle = () => {
    hapticFeedback.light();
    setShowMenu(!showMenu);
  };

  const handleCartClick = () => {
    hapticFeedback.medium();
    window.location.href = '/cart';
  };

  const handleWishlistClick = () => {
    hapticFeedback.medium();
    window.location.href = '/wishlist';
  };

  const handleProductSelect = (product: Product) => {
    hapticFeedback.medium();
    window.location.href = `/products/${product.id}`;
  };

  const handleViewAllProducts = () => {
    hapticFeedback.medium();
    window.location.href = '/products';
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      addToRecentSearches(query);
    }
  };

  const filteredProducts = newArrivals.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 relative">
      {/* Main Content */}
      <div className="relative z-10 max-w-sm mx-auto bg-white min-h-screen">
        {/* Luxury Header */}
        <div className="px-8 pt-16 pb-8">
          <div className="text-center mb-8">
            <h1 className="font-luxury text-4xl luxury-text mb-2">Discover</h1>
            <p className="font-elegant text-lg luxury-text-light">Curated Beauty Excellence</p>
          </div>

          <div className="flex items-center justify-between mb-6">
            <div>
              <span className="font-luxury-bold text-6xl luxury-text leading-none">{totalNewArrivals}</span>
              <p className="font-elegant text-sm luxury-text-light mt-1 tracking-wider uppercase">New Arrivals</p>
            </div>
            <button className="touch-target p-4 hover:bg-gray-50 rounded-full transition-all duration-300">
              <Filter className="w-5 h-5 luxury-text" />
            </button>
          </div>
        </div>

        {/* Luxury Search Bar */}
        <div className="px-8 mb-10">
          <div className="relative">
            <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 w-5 h-5 luxury-text-light" />
            <input
              type="text"
              placeholder="Search luxury beauty..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-14 pr-6 py-5 bg-white border border-gray-100 rounded-none focus:ring-1 focus:ring-gray-300 focus:border-gray-300 transition-all text-base font-elegant luxury-text shadow-sm"
              style={{ borderRadius: '2px' }}
            />
          </div>
        </div>

        {/* Luxury Categories */}
        <div className="px-8 mb-12">
          <div className="flex justify-center space-x-8">
            <button className="flex flex-col items-center space-y-3 touch-target group">
              <div className="w-16 h-16 bg-gray-50 rounded-full flex items-center justify-center group-hover:bg-gray-100 transition-colors">
                <Heart className="w-6 h-6 luxury-text-light" />
              </div>
              <span className="font-elegant text-xs luxury-text-light tracking-wider uppercase">Skincare</span>
            </button>
            <button className="flex flex-col items-center space-y-3 touch-target group">
              <div className="w-16 h-16 bg-gray-50 rounded-full flex items-center justify-center group-hover:bg-gray-100 transition-colors">
                <div className="w-6 h-6 border border-gray-400 rounded-full"></div>
              </div>
              <span className="font-elegant text-xs luxury-text-light tracking-wider uppercase">Makeup</span>
            </button>
            <button className="flex flex-col items-center space-y-3 touch-target group">
              <div className="w-16 h-16 bg-gray-50 rounded-full flex items-center justify-center group-hover:bg-gray-100 transition-colors">
                <div className="w-6 h-6 bg-gray-300 rounded-sm"></div>
              </div>
              <span className="font-elegant text-xs luxury-text-light tracking-wider uppercase">Fragrance</span>
            </button>
          </div>
        </div>

        {/* Featured Collection */}
        <div className="px-8 mb-8">
          <div className="text-center mb-8">
            <h2 className="font-luxury text-2xl luxury-text mb-2">Featured Collection</h2>
            <div className="w-12 h-px bg-gray-300 mx-auto"></div>
          </div>
        </div>

        {/* Luxury Products Grid */}
        <div className="px-8 pb-32">
          <div className="space-y-8">
            {filteredProducts.map((product, index) => (
              <LuxuryProductCard
                key={product.id}
                product={product}
                onSelect={handleProductSelect}
                isReversed={index % 2 === 1}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Luxury Bottom Navigation - NEVER scrolls */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-100">
        <div className="max-w-sm mx-auto">
          <div className="flex items-center justify-between px-8 py-6">
            <div className="flex items-center space-x-3">
              <div className="w-1.5 h-1.5 bg-black rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-gray-300 rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-gray-300 rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-gray-300 rounded-full"></div>
            </div>
            <h2 className="font-luxury text-3xl luxury-text tracking-tight">Luxora</h2>
            <button
              onClick={handleCartClick}
              className="relative touch-target p-3 bg-black hover:bg-gray-800 active:bg-gray-900 rounded-full transition-all duration-300 shadow-elegant"
            >
              <ShoppingBag className="w-5 h-5 text-white" />
              {state.items.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-black text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold border-2 border-white">
                  {state.items.length}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Luxury Product Card Component
const LuxuryProductCard: React.FC<{
  product: Product;
  onSelect: (product: Product) => void;
  isReversed?: boolean;
}> = ({ product, onSelect, isReversed = false }) => {
  const { dispatch } = useCart();
  const { hapticFeedback, showAlert } = useTelegram();

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    hapticFeedback.medium();
    dispatch({
      type: 'ADD_ITEM',
      payload: { product }
    });
    showAlert('Added to collection');
  };

  return (
    <div className={`luxury-card rounded-none p-0 overflow-hidden ${isReversed ? 'flex-row-reverse' : ''}`}>
      <div className={`flex ${isReversed ? 'flex-row-reverse' : 'flex-row'} h-40`}>
        {/* Product Image */}
        <div
          className="w-2/5 relative cursor-pointer group overflow-hidden"
          onClick={() => onSelect(product)}
        >
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
          />
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300"></div>
        </div>

        {/* Product Info */}
        <div className={`w-3/5 p-6 flex flex-col justify-between ${isReversed ? 'text-right' : 'text-left'}`}>
          <div>
            <h3 className="font-luxury text-xl luxury-text mb-2 leading-tight">{product.name}</h3>
            <p className="font-elegant text-sm luxury-text-light mb-4 leading-relaxed">
              Premium beauty essential crafted with the finest ingredients
            </p>
          </div>

          <div className={`flex items-center ${isReversed ? 'justify-end' : 'justify-start'} space-x-4`}>
            <div className={`${isReversed ? 'text-right' : 'text-left'}`}>
              {product.discount && (
                <span className="font-elegant text-sm luxury-text-light line-through block">
                  ${product.originalPrice?.toFixed(2)}
                </span>
              )}
              <span className="font-luxury text-2xl luxury-text">
                ${product.price.toFixed(2)}
              </span>
            </div>

            <button
              onClick={handleAddToCart}
              className="luxury-button px-6 py-2 text-sm font-elegant tracking-wider uppercase touch-target"
            >
              Add
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
