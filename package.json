{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:update": "jest --updateSnapshot"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}, "transform": {"^.+\\.tsx?$": ["ts-jest", {"useESM": true}]}, "extensionsToTreatAsEsm": [".ts", ".tsx"]}, "dependencies": {"@types/axios": "^0.9.36", "axios": "^1.10.0", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "dotenv": "^17.2.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "http-status": "^2.1.0", "lucide-react": "^0.344.0", "mongodb-memory-server": "^10.1.4", "react": "^18.3.1", "react-dom": "^18.3.1", "xss-clean": "^0.1.4"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@twa-dev/types": "^8.0.2", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.9", "@types/express-rate-limit": "^5.1.3", "@types/helmet": "^0.0.48", "@types/hpp": "^0.2.6", "@types/jest": "^29.5.12", "@types/node": "^24.0.12", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-router-dom": "^5.3.3", "@types/testing-library__jest-dom": "^5.14.9", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "esbuild": "^0.25.6", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "ts-jest": "^29.1.2", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.2.8"}}