import React, { useState, useEffect } from 'react';
import { ArrowLeft, Heart, ShoppingBag, Share2 } from 'lucide-react';
import { Product } from '../../types/product';
import { useCart } from '../../contexts/CartContext';
import { useTelegram } from '../../hooks/useTelegram';

interface ProductDetailProps {
  product: Product;
  onBack: () => void;
  onShare?: () => void;
}

const ProductDetail: React.FC<ProductDetailProps> = ({ product, onBack, onShare }) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedColor, setSelectedColor] = useState(product.colors?.[0] || '');
  const [isWishlisted, setIsWishlisted] = useState(false);
  const { dispatch } = useCart();
  const { hapticFeedback, cloudStorage, showAlert } = useTelegram();

  const images = product.images || [product.image];

  useEffect(() => {
    checkWishlistStatus();
  }, [product.id]);

  const checkWishlistStatus = async () => {
    const savedWishlist = await cloudStorage.getItem('wishlist');
    if (savedWishlist) {
      const wishlistIds = JSON.parse(savedWishlist);
      setIsWishlisted(wishlistIds.includes(product.id));
    }
  };

  const handleAddToCart = () => {
    hapticFeedback.medium();
    dispatch({ 
      type: 'ADD_ITEM', 
      payload: { 
        product, 
        selectedColor 
      } 
    });
    hapticFeedback.success();
    showAlert('Added to cart!');
  };

  const handleWishlistToggle = async () => {
    hapticFeedback.light();
    
    const savedWishlist = await cloudStorage.getItem('wishlist');
    let wishlistIds = savedWishlist ? JSON.parse(savedWishlist) : [];
    
    if (isWishlisted) {
      wishlistIds = wishlistIds.filter((id: string) => id !== product.id);
      setIsWishlisted(false);
      hapticFeedback.success();
    } else {
      wishlistIds.push(product.id);
      setIsWishlisted(true);
      hapticFeedback.success();
      showAlert('Added to wishlist!');
    }
    
    await cloudStorage.setItem('wishlist', JSON.stringify(wishlistIds));
  };

  const getColorClass = (color: string) => {
    const colorMap: { [key: string]: string } = {
      'Green': 'bg-green-400',
      'Blue': 'bg-blue-400',
      'Clear': 'bg-gray-200 border border-gray-300',
      'Black': 'bg-gray-900',
      'Silver': 'bg-gray-300',
      'Gold': 'bg-yellow-400',
      'Brown': 'bg-amber-600',
      'Matte Black': 'bg-gray-800',
      'Gunmetal': 'bg-gray-600',
      'Rose Gold': 'bg-pink-400'
    };
    return colorMap[color] || 'bg-gray-300';
  };

  return (
    <div className="min-h-screen bg-gray-50 animate-fade-in">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl px-6 pt-12 pb-6 border-b border-gray-100/50">
        <div className="flex items-center justify-between mb-8">
          <button
            onClick={() => {
              hapticFeedback.light();
              onBack();
            }}
            className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
          >
            <ArrowLeft className="w-6 h-6 text-gray-700" />
          </button>
          <h1 className="text-lg font-medium text-gray-600 tracking-tight">Choose Your Color</h1>
          <div className="flex items-center space-x-2">
            <button 
              onClick={handleWishlistToggle}
              className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
            >
              <Heart className={`w-6 h-6 transition-all duration-200 ${
                isWishlisted ? 'text-pink-500 fill-current' : 'text-gray-400'
              }`} />
            </button>
            {onShare && (
              <button 
                onClick={() => {
                  hapticFeedback.medium();
                  onShare();
                }}
                className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200"
              >
                <Share2 className="w-6 h-6 text-gray-700" />
              </button>
            )}
          </div>
        </div>

        {/* Main Product Image */}
        <div className="relative mb-8">
          <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-4xl p-12 flex items-center justify-center min-h-[320px] shadow-soft">
            <img
              src={images[selectedImageIndex]}
              alt={product.name}
              className="w-full max-w-xs h-52 object-cover rounded-2xl shadow-premium animate-scale-in"
            />
          </div>
          
          {/* Image Indicators */}
          <div className="flex justify-center mt-6 space-x-2">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  hapticFeedback.selection();
                  setSelectedImageIndex(index);
                }}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${
                  selectedImageIndex === index 
                    ? 'bg-green-500 scale-125' 
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Color Selection Circles */}
        {product.colors && (
          <div className="mb-8">
            <div className="flex justify-center space-x-4">
              {product.colors.map((color) => (
                <button
                  key={color}
                  onClick={() => {
                    hapticFeedback.selection();
                    setSelectedColor(color);
                  }}
                  className={`w-16 h-16 rounded-full border-4 transition-all duration-200 hover:scale-110 ${
                    selectedColor === color 
                      ? 'border-green-500 scale-110 shadow-lg' 
                      : 'border-gray-200 hover:border-gray-300'
                  } ${getColorClass(color)}`}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="bg-white mt-2 px-6 py-8 rounded-t-4xl shadow-premium">
        <div className="flex items-start justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-3 tracking-tight font-display">{product.name}</h2>
            {product.sku && (
              <span className="inline-block bg-gray-100 text-gray-600 px-3 py-1.5 rounded-xl text-sm font-medium tracking-tight">
                {product.sku}
              </span>
            )}
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold text-gray-900 tracking-tight">${product.price.toFixed(2)}</div>
            {product.originalPrice && (
              <div className="text-lg text-gray-400 line-through font-medium">
                ${product.originalPrice.toFixed(2)}
              </div>
            )}
          </div>
        </div>

        {/* Specifications */}
        {product.specifications && (
          <div className="grid grid-cols-3 gap-6 mb-10">
            <div>
              <div className="text-gray-500 text-sm mb-2 font-medium">Height:</div>
              <div className="font-semibold text-gray-900 tracking-tight">{product.specifications.height}</div>
            </div>
            <div>
              <div className="text-gray-500 text-sm mb-2 font-medium">Width:</div>
              <div className="font-semibold text-gray-900 tracking-tight">{product.specifications.width}</div>
            </div>
            <div>
              <div className="text-gray-500 text-sm mb-2 font-medium">Material:</div>
              <div className="font-semibold text-gray-900 tracking-tight">{product.specifications.material}</div>
            </div>
          </div>
        )}

        {/* Add to Cart Button */}
        <button
          onClick={handleAddToCart}
          className="w-full bg-green-600 hover:bg-green-700 text-white py-4 rounded-2xl text-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-3 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
        >
          <ShoppingBag className="w-5 h-5" />
          <span>Add to Cart</span>
        </button>
      </div>
    </div>
  );
};

export default ProductDetail;