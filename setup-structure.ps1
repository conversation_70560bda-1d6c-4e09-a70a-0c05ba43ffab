# Create main directories
$directories = @(
    "src\assets\fonts",
    "src\assets\images",
    "src\assets\icons",
    "src\components\common",
    "src\components\layout",
    "src\components\ui",
    "src\features\auth",
    "src\features\products",
    "src\features\cart",
    "src\features\checkout",
    "src\features\account",
    "src\features\profile",
    "src\hooks",
    "src\lib",
    "src\services",
    "src\stores",
    "src\types",
    "src\utils",
    "src\config",
    "src\constants",
    "src\contexts"
)

foreach ($dir in $directories) {
    if (-not (Test-Path -Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir"
    } else {
        Write-Host "Directory already exists: $dir"
    }
}

Write-Host "Project structure created successfully!"
