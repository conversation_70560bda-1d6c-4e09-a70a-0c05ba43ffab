import { useState, useEffect, useMemo, useCallback } from 'react';
import { Product, ProductFilter, ProductSortOption } from '../types/product';
import { products } from '../data/products';
import { APP_CONFIG } from '../config/app';

interface SearchState {
  query: string;
  results: Product[];
  isLoading: boolean;
  suggestions: string[];
  recentSearches: string[];
  filters: ProductFilter;
  sortBy: ProductSortOption;
  totalResults: number;
}

interface UseProductSearchReturn extends SearchState {
  search: (query: string) => void;
  clearSearch: () => void;
  addFilter: (filter: Partial<ProductFilter>) => void;
  removeFilter: (filterKey: keyof ProductFilter) => void;
  clearFilters: () => void;
  setSortBy: (sort: ProductSortOption) => void;
  addToRecentSearches: (query: string) => void;
  clearRecentSearches: () => void;
  getSuggestions: (query: string) => string[];
}

const RECENT_SEARCHES_KEY = 'souq_recent_searches';

export const useProductSearch = (): UseProductSearchReturn => {
  const [state, setState] = useState<SearchState>({
    query: '',
    results: [],
    isLoading: false,
    suggestions: [],
    recentSearches: [],
    filters: {},
    sortBy: 'newest',
    totalResults: 0,
  });

  // Load recent searches from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem(RECENT_SEARCHES_KEY);
    if (saved) {
      try {
        const recentSearches = JSON.parse(saved);
        setState(prev => ({ ...prev, recentSearches }));
      } catch (error) {
        console.error('Failed to load recent searches:', error);
      }
    }
  }, []);

  // Generate search suggestions based on products
  const generateSuggestions = useCallback((query: string): string[] => {
    if (!query.trim()) return [];

    const lowerQuery = query.toLowerCase();
    const suggestions = new Set<string>();

    // Add product names that match
    products.forEach(product => {
      if (product.name.toLowerCase().includes(lowerQuery)) {
        suggestions.add(product.name);
      }
      
      // Add brand names that match
      if (product.brand?.toLowerCase().includes(lowerQuery)) {
        suggestions.add(product.brand);
      }
      
      // Add tags that match
      product.tags.forEach(tag => {
        if (tag.toLowerCase().includes(lowerQuery)) {
          suggestions.add(tag);
        }
      });
      
      // Add category if it matches
      if (product.category.toLowerCase().includes(lowerQuery)) {
        suggestions.add(product.category);
      }
    });

    return Array.from(suggestions).slice(0, 5);
  }, []);

  // Filter and sort products based on current state
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = [...products];

    // Apply search filter
    if (state.query.trim()) {
      const query = state.query.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.description.toLowerCase().includes(query) ||
        product.shortDescription?.toLowerCase().includes(query) ||
        product.brand?.toLowerCase().includes(query) ||
        product.tags.some(tag => tag.toLowerCase().includes(query)) ||
        product.category.toLowerCase().includes(query) ||
        product.sku.toLowerCase().includes(query)
      );
    }

    // Apply category filter
    if (state.filters.categories?.length) {
      filtered = filtered.filter(product =>
        state.filters.categories!.includes(product.category)
      );
    }

    // Apply price range filter
    if (state.filters.priceRange) {
      filtered = filtered.filter(product =>
        product.price >= state.filters.priceRange!.min &&
        product.price <= state.filters.priceRange!.max
      );
    }

    // Apply rating filter
    if (state.filters.rating) {
      filtered = filtered.filter(product =>
        product.rating >= state.filters.rating!
      );
    }

    // Apply brand filter
    if (state.filters.brands?.length) {
      filtered = filtered.filter(product =>
        product.brand && state.filters.brands!.includes(product.brand)
      );
    }

    // Apply stock filter
    if (state.filters.inStock) {
      filtered = filtered.filter(product => product.stock > 0);
    }

    // Apply sale filter
    if (state.filters.onSale) {
      filtered = filtered.filter(product => product.discount && product.discount > 0);
    }

    // Apply new filter
    if (state.filters.isNew) {
      filtered = filtered.filter(product => product.isNew);
    }

    // Apply featured filter
    if (state.filters.isFeatured) {
      filtered = filtered.filter(product => product.isFeatured);
    }

    // Apply sorting
    switch (state.sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        break;
      case 'price-low-high':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high-low':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating-high-low':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'rating-low-high':
        filtered.sort((a, b) => a.rating - b.rating);
        break;
      case 'name-a-z':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'name-z-a':
        filtered.sort((a, b) => b.name.localeCompare(a.name));
        break;
      case 'popularity':
        filtered.sort((a, b) => b.reviewCount - a.reviewCount);
        break;
      default:
        break;
    }

    return filtered;
  }, [state.query, state.filters, state.sortBy]);

  // Update results when filtered products change
  useEffect(() => {
    setState(prev => ({
      ...prev,
      results: filteredAndSortedProducts,
      totalResults: filteredAndSortedProducts.length,
    }));
  }, [filteredAndSortedProducts]);

  // Search function
  const search = useCallback((query: string) => {
    setState(prev => ({
      ...prev,
      query,
      isLoading: true,
    }));

    // Simulate search delay
    setTimeout(() => {
      setState(prev => ({
        ...prev,
        isLoading: false,
        suggestions: generateSuggestions(query),
      }));
    }, 300);
  }, [generateSuggestions]);

  // Clear search
  const clearSearch = useCallback(() => {
    setState(prev => ({
      ...prev,
      query: '',
      results: [],
      suggestions: [],
      totalResults: 0,
    }));
  }, []);

  // Add filter
  const addFilter = useCallback((filter: Partial<ProductFilter>) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...filter },
    }));
  }, []);

  // Remove filter
  const removeFilter = useCallback((filterKey: keyof ProductFilter) => {
    setState(prev => {
      const newFilters = { ...prev.filters };
      delete newFilters[filterKey];
      return { ...prev, filters: newFilters };
    });
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setState(prev => ({ ...prev, filters: {} }));
  }, []);

  // Set sort option
  const setSortBy = useCallback((sortBy: ProductSortOption) => {
    setState(prev => ({ ...prev, sortBy }));
  }, []);

  // Add to recent searches
  const addToRecentSearches = useCallback((query: string) => {
    if (!query.trim()) return;

    setState(prev => {
      const newRecentSearches = [
        query,
        ...prev.recentSearches.filter(s => s !== query)
      ].slice(0, APP_CONFIG.RECENT_SEARCHES_LIMIT);

      // Save to localStorage
      localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(newRecentSearches));

      return { ...prev, recentSearches: newRecentSearches };
    });
  }, []);

  // Clear recent searches
  const clearRecentSearches = useCallback(() => {
    setState(prev => ({ ...prev, recentSearches: [] }));
    localStorage.removeItem(RECENT_SEARCHES_KEY);
  }, []);

  // Get suggestions for a query
  const getSuggestions = useCallback((query: string) => {
    return generateSuggestions(query);
  }, [generateSuggestions]);

  return {
    ...state,
    search,
    clearSearch,
    addFilter,
    removeFilter,
    clearFilters,
    setSortBy,
    addToRecentSearches,
    clearRecentSearches,
    getSuggestions,
  };
};
