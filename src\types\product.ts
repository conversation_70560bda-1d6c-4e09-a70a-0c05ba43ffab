export interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  images?: string[];
  category: string;
  description: string;
  rating: number;
  reviews: number;
  discount?: number;
  isSpecialOffer?: boolean;
  isFavorite?: boolean;
  sku?: string;
  specifications?: {
    height?: string;
    width?: string;
    material?: string;
    frameType?: string;
    lensType?: string;
  };
  colors?: string[];
}

export interface CartItem {
  product: Product;
  quantity: number;
  selectedColor?: string;
}

export interface Deal {
  id: string;
  title: string;
  products: Product[];
  discount: number;
}