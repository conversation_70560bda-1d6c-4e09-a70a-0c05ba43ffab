// Product Category Types
export type ProductCategory =
  | 'sunglasses'
  | 'eyeglasses'
  | 'contact-lenses'
  | 'accessories'
  | 'skincare'
  | 'makeup'
  | 'fragrance'
  | 'hair-care'
  | 'tools';

// Product Status Types
export type ProductStatus = 'active' | 'inactive' | 'out-of-stock' | 'discontinued';

// Product Condition Types
export type ProductCondition = 'new' | 'refurbished' | 'used';

// Enhanced Product Interface
export interface Product {
  id: string;
  vendorId?: string;
  name: string;
  description: string;
  shortDescription?: string;
  price: number;
  originalPrice?: number;
  currency: string;
  image: string;
  images: string[];
  category: ProductCategory;
  subcategory?: string;
  brand?: string;
  sku: string;
  stock: number;
  minStock?: number;
  rating: number;
  reviewCount: number;
  discount?: number;
  isSpecialOffer?: boolean;
  isFeatured?: boolean;
  isNew?: boolean;
  status: ProductStatus;
  condition: ProductCondition;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
    unit: 'cm' | 'mm' | 'in';
  };
  specifications: Record<string, any>;
  tags: string[];
  colors?: string[];
  sizes?: string[];
  variants?: ProductVariant[];
  seoTitle?: string;
  seoDescription?: string;
  createdAt: string;
  updatedAt: string;
}

// Product Variant Interface (for different colors, sizes, etc.)
export interface ProductVariant {
  id: string;
  productId: string;
  name: string;
  sku: string;
  price?: number;
  stock: number;
  attributes: Record<string, string>; // e.g., { color: 'red', size: 'M' }
  images?: string[];
}

// Enhanced Cart Item Interface
export interface CartItem {
  id: string;
  product: Product;
  variant?: ProductVariant;
  quantity: number;
  selectedAttributes?: Record<string, string>;
  addedAt: string;
}

// Product Review Interface
export interface ProductReview {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title?: string;
  comment: string;
  images?: string[];
  verified: boolean;
  helpful: number;
  createdAt: string;
  updatedAt: string;
}

// Deal Interface
export interface Deal {
  id: string;
  title: string;
  description?: string;
  products: Product[];
  discount: number;
  discountType: 'percentage' | 'fixed';
  startDate: string;
  endDate: string;
  isActive: boolean;
  maxUses?: number;
  currentUses: number;
}

// Product Filter Interface
export interface ProductFilter {
  categories?: ProductCategory[];
  priceRange?: {
    min: number;
    max: number;
  };
  rating?: number;
  brands?: string[];
  inStock?: boolean;
  onSale?: boolean;
  isNew?: boolean;
  isFeatured?: boolean;
}

// Product Sort Options
export type ProductSortOption =
  | 'newest'
  | 'oldest'
  | 'price-low-high'
  | 'price-high-low'
  | 'rating-high-low'
  | 'rating-low-high'
  | 'name-a-z'
  | 'name-z-a'
  | 'popularity';

// Search Result Interface
export interface ProductSearchResult {
  products: Product[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  filters: {
    categories: Array<{ category: ProductCategory; count: number }>;
    brands: Array<{ brand: string; count: number }>;
    priceRange: { min: number; max: number };
  };
}