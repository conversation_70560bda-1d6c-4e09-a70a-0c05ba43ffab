import React, { useState, useEffect, useCallback } from 'react';
import { CartProvider } from './contexts/CartContext';
import { useTelegram } from './hooks/useTelegram';
import HeroSection from './components/Hero/HeroSection';
import Home from './components/Home/Home';
import Cart from './components/Cart/Cart';
import ProductDetail from './components/Product/ProductDetail';
import Settings from './components/Settings/Settings';
import Wishlist from './components/Wishlist/Wishlist';
import ShareProduct from './components/Share/ShareProduct';
import { Product } from './types/product';

type Screen = 'hero' | 'home' | 'cart' | 'product' | 'settings' | 'wishlist';

// Error Boundary Component
class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  // Show loading state while checking auth and Telegram initialization
  if (isLoading || !isReady) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <Routes>
      {/* Public routes */}
      <Route path="/" element={<HomePage />} />
      <Route path="/products" element={<ProductsPage />} />
      <Route path="/products/:id" element={<ProductDetailPage />} />
      
      {/* Auth routes */}
      <Route
        path="/login"
        element={
          !isAuthenticated ? (
            <LoginPage />
          ) : (
            <Navigate to="/" replace />
          )
        }
      />
      <Route
        path="/register"
        element={
          !isAuthenticated ? (
            <RegisterPage />
          ) : (
            <Navigate to="/" replace />
          )
        }
      />

      {/* Protected routes */}
      <Route
        path="/cart"
        element={
          <ProtectedRoute>
            <CartPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/checkout"
        element={
          <ProtectedRoute>
            <CheckoutPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/profile"
        element={
          <ProtectedRoute>
            <ProfilePage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/orders"
        element={
          <ProtectedRoute>
            <OrdersPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/orders/:id"
        element={
          <ProtectedRoute>
            <OrderDetailPage />
          </ProtectedRoute>
        }
      />

      {/* Admin routes */}
      <Route
        path="/admin/*"
        element={
          <ProtectedRoute roles={['admin']}>
            <div>Admin Dashboard</div>
          </ProtectedRoute>
        }
      />

      {/* 404 - Not Found */}
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

// Main App component
function App() {
  const [activeScreen, setActiveScreen] = useState<Screen>('hero');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const { 
    tg, 
    user, 
    isReady, 
    theme, 
    viewportHeight,
    isExpanded,
    hapticFeedback,
    mainButton,
    backButton,
    showAlert,
    showConfirm,
    closeApp,
    isVersionAtLeast
  } = useTelegram();

  // Apply theme classes to body
  useEffect(() => {
    document.body.className = theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-gray-900';
  }, [theme]);

  // Set up viewport height for mobile
  useEffect(() => {
    const setAppHeight = () => {
      document.documentElement.style.setProperty('--app-height', `${window.innerHeight}px`);
    };
    
    setAppHeight();
    window.addEventListener('resize', setAppHeight);
    return () => window.removeEventListener('resize', setAppHeight);
  }, []);

  // Initialize Telegram WebApp features
  useEffect(() => {
    if (!isReady || !tg) return;

    // Enable closing confirmation
    tg.enableClosingConfirmation();

    // Set up back button
    const handleBackButton = () => {
      hapticFeedback.impact('light');
      if (activeScreen === 'product' || activeScreen === 'cart' || activeScreen === 'settings' || activeScreen === 'wishlist') {
        setActiveScreen('home');
        setSelectedProduct(null);
      } else if (activeScreen === 'home') {
        setActiveScreen('hero');
      }
    };

    // Set up main button
    const updateMainButton = () => {
      if (activeScreen === 'cart') {
        mainButton.setParams({
          text: 'Proceed to Checkout',
          color: '#16a34a',
          text_color: '#ffffff',
          is_visible: true,
          is_active: true
        });
      } else if (activeScreen === 'product' && selectedProduct) {
        mainButton.setParams({
          text: `Add to Cart - $${selectedProduct.price.toFixed(2)}`,
          color: '#3b82f6',
          text_color: '#ffffff',
          is_visible: true,
          is_active: true
        });
      } else {
        mainButton.hide();
      }
    };

    // Set up back button visibility
    if (activeScreen !== 'hero') {
      backButton.show();
    } else {
      backButton.hide();
    }

    // Update main button when screen changes
    updateMainButton();

    // Clean up event listeners
    const cleanupBackButton = backButton.onClick(handleBackButton);
    
    return () => {
      cleanupBackButton();
      mainButton.hide();
    };
  }, [tg, isReady, activeScreen, selectedProduct, hapticFeedback, mainButton, backButton]);

  // Handle deep linking
  useEffect(() => {
    if (!isReady || !tg?.initDataUnsafe?.start_param) return;

    const startParam = tg.initDataUnsafe.start_param;
    if (startParam.startsWith('product_')) {
      const productId = startParam.replace('product_', '');
      // In a real app, you would fetch the product by ID
      console.log('Deep link to product:', productId);
      // setSelectedProduct(fetchProductById(productId));
      setActiveScreen('product');
    }
  }, [tg, isReady]);

  const handleGoShopping = () => {
    hapticFeedback.impact('medium');
    setActiveScreen('home');
  };

  const handleProductSelect = (product: Product) => {
    hapticFeedback.impact('light');
    setSelectedProduct(product);
    setActiveScreen('product');
  };

  const handleBackToHome = () => {
    hapticFeedback.impact('light');
    setActiveScreen('home');
  };

  const handleAddToCart = (product: Product) => {
    hapticFeedback.notification('success');
    // Add to cart logic here
    console.log('Added to cart:', product);
    showAlert('Added to cart!');
  };

  const handleCheckout = async () => {
    if (activeScreen === 'cart') {
      const confirmed = await showConfirm('Proceed to checkout?');
      if (confirmed) {
        // Handle checkout logic
        console.log('Proceeding to checkout');
      }
    } else if (activeScreen === 'product' && selectedProduct) {
      handleAddToCart(selectedProduct);
    }
  };

  // Set up main button click handler
  useEffect(() => {
    if (!isReady) return;

    const cleanup = mainButton.onClick(handleCheckout);
    return cleanup;
  }, [isReady, handleCheckout, mainButton]);

  // Handle app close request
  const handleClose = useCallback(() => {
    showConfirm('Are you sure you want to exit?').then((confirmed) => {
      if (confirmed) {
        closeApp();
      }
    });
  }, [closeApp, showConfirm]);

  // Show loading state while initializing
  if (!isReady) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
    hapticFeedback.light();
    setActiveScreen('home');
    setSelectedProduct(null);
  };

  const handleGoToCart = () => {
    hapticFeedback.medium();
    setActiveScreen('cart');
  };

  const handleGoToWishlist = () => {
    hapticFeedback.medium();
    setActiveScreen('wishlist');
  };

  const handleShareProduct = (product: Product) => {
    hapticFeedback.medium();
    setSelectedProduct(product);
    setShowShareModal(true);
  };

  const renderScreen = () => {
    switch (activeScreen) {
      case 'hero':
        return <HeroSection onGoShopping={handleGoShopping} />;
      case 'home':
        return <Home onProductSelect={handleProductSelect} onGoToCart={handleGoToCart} onGoToWishlist={handleGoToWishlist} />;
      case 'cart':
        return <Cart onBack={handleBackToHome} />;
      case 'settings':
        return <Settings onBack={handleBackToHome} />;
      case 'wishlist':
        return <Wishlist onBack={handleBackToHome} onProductSelect={handleProductSelect} />;
      case 'product':
        return selectedProduct ? (
          <ProductDetail 
            product={selectedProduct} 
            onBack={handleBackToHome}
            onShare={() => handleShareProduct(selectedProduct)}
          />
        ) : (
          <Home onProductSelect={handleProductSelect} />
        );
      default:
        return <HeroSection onGoShopping={handleGoShopping} />;
    }
  };

  return (
    <CartProvider>
      <div className="min-h-screen bg-gray-50" style={{ minHeight: 'calc(var(--vh, 1vh) * 100)' }}>
        {renderScreen()}
        
        {/* Floating Action Buttons */}

        {/* Share Modal */}
        {showShareModal && selectedProduct && (
          <ShareProduct
            product={selectedProduct}
            onClose={() => setShowShareModal(false)}
          />
        )}
      </div>
    </CartProvider>
  );
}

export default App;