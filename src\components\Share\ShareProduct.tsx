import React from 'react';
import { MessageCircle, Copy, ExternalLink } from 'lucide-react';
import { Product } from '../../types/product';
import { useTelegram } from '../../hooks/useTelegram.new';

interface ShareProductProps {
  product: Product;
  onClose: () => void;
}

const ShareProduct: React.FC<ShareProductProps> = ({ product, onClose }) => {
  const { tg, showAlert } = useTelegram();

  const shareText = `Check out this amazing ${product.name} for just $${product.price}! 🕶️✨`;
  const shareUrl = `https://t.me/your_bot?start=product_${product.id}`;

  const handleShareToChat = () => {
    tg?.HapticFeedback.impactOccurred('medium');
    // Use the native share API if available, otherwise fallback to clipboard
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: shareText,
        url: shareUrl,
      }).catch(console.error);
    } else {
      navigator.clipboard.writeText(`${shareText}\n\n${shareUrl}`);
      showAlert('Link copied to clipboard!');
    }
    onClose();
  };

  const handleCopyLink = async () => {
    tg?.HapticFeedback.impactOccurred('light');
    try {
      await navigator.clipboard.writeText(shareUrl);
      showAlert('Link copied to clipboard!');
      tg?.HapticFeedback.notificationOccurred('success');
    } catch {
      tg?.HapticFeedback.notificationOccurred('error');
    }
  };

  const handleOpenExternal = () => {
    tg?.HapticFeedback.impactOccurred('medium');
    tg?.openLink(shareUrl);
    onClose();
  };

  const shareOptions = [
    {
      id: 'chat',
      icon: <MessageCircle className="w-5 h-5" />,
      label: 'Share to Chat',
      onClick: handleShareToChat,
    },
    {
      id: 'copy',
      icon: <Copy className="w-5 h-5" />,
      label: 'Copy Link',
      onClick: handleCopyLink,
    },
    {
      id: 'external',
      icon: <ExternalLink className="w-5 h-5" />,
      label: 'Open in Browser',
      onClick: handleOpenExternal,
    },
  ];

  return (
    <div className="p-4">
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
        Share {product.name}
      </h3>
      <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg flex items-center space-x-3">
        <img
          src={product.image}
          alt={product.name}
          className="w-16 h-16 object-cover rounded-lg"
        />
        <div>
          <h4 className="font-medium text-gray-900 dark:text-gray-100">{product.name}</h4>
          <p className="text-sm text-gray-600 dark:text-gray-300">${product.price}</p>
        </div>
      </div>
      <div className="grid grid-cols-3 gap-4">
        {shareOptions.map((option) => (
          <button
            key={option.id}
            type="button"
            onClick={option.onClick}
            className="flex flex-col items-center justify-center p-3 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <div className="w-10 h-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400 mb-2">
              {option.icon}
            </div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {option.label}
            </span>
          </button>
        ))}
      </div>
      <div className="mt-6 flex justify-end">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100"
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default ShareProduct;