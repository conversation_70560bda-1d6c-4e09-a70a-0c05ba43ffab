import React, { useState } from 'react';
import { ArrowLeft, Heart, ShoppingBag } from 'lucide-react';
import { useParams, useNavigate } from 'react-router-dom';
import { products } from '../data/products';
import { useCart } from '../contexts/CartContext';
import { useTelegram } from '../hooks/useTelegram';

const ProductDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { dispatch } = useCart();
  const { hapticFeedback, showAlert } = useTelegram();
  
  const product = products.find(p => p.id === id);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isWishlisted, setIsWishlisted] = useState(false);

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Product not found</h2>
          <button 
            onClick={() => navigate('/')}
            className="text-green-600 hover:text-green-700"
          >
            Go back to home
          </button>
        </div>
      </div>
    );
  }

  const handleBack = () => {
    hapticFeedback.light();
    navigate(-1);
  };

  const handleWishlistToggle = () => {
    hapticFeedback.light();
    setIsWishlisted(!isWishlisted);
    showAlert(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist');
  };

  const handleAddToCart = () => {
    hapticFeedback.medium();
    dispatch({ 
      type: 'ADD_ITEM', 
      payload: { product } 
    });
    showAlert('Added to cart!');
  };

  // Mock additional images for the design
  const productImages = [
    product.image,
    product.image, // In real app, these would be different angles
    product.image,
  ];

  const modelImages = [
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
    'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-green-100 to-green-200 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200 rounded-full opacity-30"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-green-300 rounded-full opacity-20"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-sm mx-auto bg-white/95 backdrop-blur-sm min-h-screen mobile-scroll">
        {/* Header */}
        <div className="flex items-center justify-between p-6 pt-16">
          <button
            onClick={handleBack}
            className="touch-target p-3 hover:bg-gray-100 rounded-xl transition-colors"
          >
            <ArrowLeft className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-xl font-semibold text-gray-700 tracking-tight">Choose Your Color</h1>
          <button
            onClick={handleWishlistToggle}
            className="touch-target p-3 hover:bg-gray-100 rounded-xl transition-colors"
          >
            <Heart className={`w-6 h-6 ${isWishlisted ? 'text-pink-500 fill-current' : 'text-gray-600'}`} />
          </button>
        </div>

        {/* Main Product Image */}
        <div className="px-6 mb-8">
          <div className="relative">
            <img
              src={productImages[selectedImageIndex]}
              alt={product.name}
              className="w-full h-64 object-cover rounded-3xl"
            />
            {/* Color indicator dots */}
            <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2">
              <div className="flex space-x-2">
                {productImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`w-2 h-2 rounded-full transition-all ${
                      index === selectedImageIndex 
                        ? 'bg-gray-800 w-6' 
                        : 'bg-gray-400'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Model Images */}
        <div className="px-6 mb-8">
          <div className="flex space-x-4">
            {modelImages.map((image, index) => (
              <div key={index} className="w-16 h-16 rounded-full overflow-hidden">
                <img
                  src={image}
                  alt={`Model ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Product Info */}
        <div className="px-6 mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-gray-900">{product.name}</h2>
            <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
              ER02542
            </span>
          </div>
          
          <div className="flex items-center space-x-3 mb-6">
            <span className="text-2xl font-bold text-gray-900">
              ${product.price.toFixed(2)}
            </span>
            {product.originalPrice && (
              <span className="text-lg text-gray-400 line-through">
                ${product.originalPrice.toFixed(2)}
              </span>
            )}
          </div>

          {/* Specifications */}
          <div className="grid grid-cols-3 gap-4 mb-8">
            <div>
              <p className="text-sm text-gray-500 mb-1">Height:</p>
              <p className="font-semibold text-gray-900">4 cm</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-1">Width:</p>
              <p className="font-semibold text-gray-900">15 cm</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-1">Material</p>
              <p className="font-semibold text-gray-900">Glass</p>
            </div>
          </div>
        </div>

        {/* Add to Cart Button */}
        <div className="px-6 pb-24">
          <button
            onClick={handleAddToCart}
            className="w-full bg-green-800 hover:bg-green-900 text-white py-4 rounded-2xl font-semibold text-lg transition-colors"
          >
            Add to Cart
          </button>
        </div>
      </div>

      {/* Fixed Bottom Navigation - NEVER scrolls */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white/98 backdrop-blur-xl border-t border-gray-100 safe-area-inset-bottom">
        <div className="max-w-sm mx-auto">
          <div className="flex items-center justify-between px-6 py-5">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-900 rounded-full"></div>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 tracking-tight">Luxora</h2>
            <button
              onClick={() => navigate('/cart')}
              className="relative touch-target p-3 bg-pink-500 hover:bg-pink-600 active:bg-pink-700 rounded-2xl transition-colors shadow-lg"
            >
              <ShoppingBag className="w-6 h-6 text-white" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;
