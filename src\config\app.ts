// App configuration
export const APP_CONFIG = {
  APP_NAME: 'Souq',
  APP_DESCRIPTION: 'Telegram Mini App E-commerce Platform for Ethiopia',
  DEFAULT_LANGUAGE: 'en' as const,
  SUPPORTED_LANGUAGES: [
    { code: 'en', name: 'English' },
    { code: 'am', name: 'አማርኛ' },
  ],
  CURRENCY: 'ETB',
  CURRENCY_SYMBOL: 'Br',
  DEFAULT_THEME: 'light' as const,
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  TELEGRAM_BOT_NAME: import.meta.env.VITE_TELEGRAM_BOT_NAME || 'souq_eth_bot',
  TELEGRAM_WEB_APP_VERSION: '6.1',
  CART_STORAGE_KEY: 'souq_cart',
  AUTH_TOKEN_KEY: 'souq_auth_token',
  REFRESH_TOKEN_KEY: 'souq_refresh_token',
  THEME_STORAGE_KEY: 'souq_theme',
  LANGUAGE_STORAGE_KEY: 'souq_language',
  MAX_PRODUCT_IMAGES: 5,
  MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  PAGINATION_LIMIT: 10,
  RECENT_SEARCHES_LIMIT: 5,
  RECENT_VIEWED_PRODUCTS_LIMIT: 10,
  AUTO_LOGOUT_TIME: 30 * 60 * 1000, // 30 minutes
  TOKEN_REFRESH_INTERVAL: 15 * 60 * 1000, // 15 minutes
  NOTIFICATION_TIMEOUT: 5000, // 5 seconds
  DEBOUNCE_DELAY: 300, // ms
  THROTTLE_DELAY: 1000, // ms
  MAP_ZOOM_LEVEL: 15,
  DEFAULT_LOCATION: {
    lat: 9.145,
    lng: 40.4897,
  },
  IS_DEVELOPMENT: import.meta.env.DEV,
  IS_PRODUCTION: import.meta.env.PROD,
} as const;

// Feature flags
export const FEATURE_FLAGS = {
  ENABLE_PAYMENTS: true,
  ENABLE_NOTIFICATIONS: true,
  ENABLE_ANALYTICS: false,
  ENABLE_OFFLINE_MODE: true,
  ENABLE_PUSH_NOTIFICATIONS: false,
} as const;
