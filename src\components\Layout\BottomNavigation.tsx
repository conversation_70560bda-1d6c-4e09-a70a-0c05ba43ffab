import React from 'react';
import { Home, CreditCard, ShoppingCart, MessageSquare, Gift } from 'lucide-react';

interface BottomNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({ activeTab, onTabChange }) => {
  const tabs = [
    { id: 'home', icon: Home, label: 'Home' },
    { id: 'orders', icon: CreditCard, label: 'Orders' },
    { id: 'cart', icon: ShoppingCart, label: 'Cart' },
    { id: 'chat', icon: MessageSquare, label: 'Chat' },
    { id: 'deals', icon: Gift, label: 'Deals' }
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-black rounded-t-3xl px-4 py-3 mx-4 mb-4">
      <div className="flex items-center justify-around">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          const isActive = activeTab === tab.id;
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`flex items-center justify-center p-2 rounded-full transition-all ${
                isActive
                  ? 'bg-green-500 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              <Icon className="w-6 h-6" />
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default BottomNavigation;