import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Plus, Minus, Trash2, ShoppingBag } from 'lucide-react';
import { useCart } from '../../contexts/CartContext';
import { useTelegram } from '../../hooks/useTelegram';

const CartPage: React.FC = () => {
  const navigate = useNavigate();
  const { state, dispatch } = useCart();

  // Safe Telegram hook usage
  let hapticFeedback, mainButton;
  try {
    const telegram = useTelegram();
    hapticFeedback = telegram.hapticFeedback;
    mainButton = telegram.mainButton;
  } catch (error) {
    // Fallback for when Telegram is not available
    hapticFeedback = {
      light: () => {},
      medium: () => {},
      heavy: () => {},
      success: () => {}
    };
    mainButton = {
      setText: () => {},
      show: () => {},
      hide: () => {},
      onClick: () => {},
      offClick: () => {}
    };
  }

  const handleBack = () => {
    hapticFeedback.light();
    navigate(-1);
  };

  const handleUpdateQuantity = (productId: string, newQuantity: number) => {
    hapticFeedback.light();
    if (newQuantity <= 0) {
      dispatch({ type: 'REMOVE_ITEM', payload: productId });
    } else {
      dispatch({ 
        type: 'UPDATE_QUANTITY', 
        payload: { id: productId, quantity: newQuantity } 
      });
    }
  };

  const handleRemoveItem = (productId: string) => {
    hapticFeedback.medium();
    dispatch({ type: 'REMOVE_ITEM', payload: productId });
  };

  const handleCheckout = () => {
    hapticFeedback.medium();
    navigate('/checkout');
  };

  const handleCartClick = () => {
    // Already on cart page, do nothing or scroll to top
    hapticFeedback.light();
  };

  // Configure main button
  React.useEffect(() => {
    try {
      if (state.items.length > 0) {
        mainButton.setText(`Checkout • $${state.total.toFixed(2)}`);
        mainButton.show();
        mainButton.onClick(handleCheckout);
      } else {
        mainButton.hide();
      }

      return () => {
        mainButton.offClick(handleCheckout);
      };
    } catch (error) {
      console.log('Telegram main button not available');
    }
  }, [state.items.length, state.total, mainButton, handleCheckout]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 relative">
      {/* Main Content */}
      <div className="relative z-10 max-w-sm mx-auto bg-white min-h-screen pb-24">
        {/* Luxury Header */}
        <div className="px-8 pt-16 pb-8">
          <div className="flex items-center justify-between mb-8">
            <button
              onClick={handleBack}
              className="touch-target p-4 hover:bg-gray-50 rounded-full transition-all duration-300"
            >
              <ArrowLeft className="w-5 h-5 luxury-text" />
            </button>
            <div className="text-center">
              <h1 className="font-luxury text-3xl luxury-text mb-1">Your Collection</h1>
              <div className="w-8 h-px bg-gray-300 mx-auto"></div>
            </div>
            <div className="w-12"></div> {/* Spacer for centering */}
          </div>
        </div>

      {state.items.length === 0 ? (
        /* Empty Collection */
        <div className="flex flex-col items-center justify-center min-h-[60vh] px-8">
          <div className="luxury-text-light mb-8">
            <ShoppingBag className="w-20 h-20 mx-auto" />
          </div>
          <h2 className="font-luxury text-2xl luxury-text mb-4">Your collection awaits</h2>
          <p className="font-elegant luxury-text-light text-center mb-8 leading-relaxed">
            Discover our curated selection of premium beauty essentials
          </p>
          <button
            onClick={() => navigate('/products')}
            className="luxury-button px-8 py-4 text-sm font-elegant tracking-wider uppercase"
          >
            Explore Collection
          </button>
        </div>
      ) : (
        /* Collection Items */
        <div className="px-8">
          <div className="space-y-6 mb-8">
            {state.items.map((item) => (
              <div key={`${item.product.id}-${item.selectedColor}`} className="luxury-card rounded-none p-0 overflow-hidden">
                <div className="flex h-32">
                  {/* Product Image */}
                  <div className="w-32 flex-shrink-0">
                    <img
                      src={item.product.image}
                      alt={item.product.name}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Product Info */}
                  <div className="flex-1 p-6 flex flex-col justify-between">
                    <div>
                      <h3 className="font-luxury text-lg luxury-text mb-1">
                        {item.product.name}
                      </h3>
                      {item.selectedColor && (
                        <p className="font-elegant text-sm luxury-text-light">Color: {item.selectedColor}</p>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="font-luxury text-xl luxury-text">
                        ${item.product.price.toFixed(2)}
                      </span>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleUpdateQuantity(item.product.id, item.quantity - 1)}
                            className="w-8 h-8 border border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50 transition-colors"
                          >
                            <Minus className="w-3 h-3" />
                          </button>
                          <span className="font-elegant text-sm luxury-text w-6 text-center">
                            {item.quantity}
                          </span>
                          <button
                            onClick={() => handleUpdateQuantity(item.product.id, item.quantity + 1)}
                            className="w-8 h-8 border border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50 transition-colors"
                          >
                            <Plus className="w-3 h-3" />
                          </button>
                        </div>

                        <button
                          onClick={() => handleRemoveItem(item.product.id)}
                          className="p-2 luxury-text-light hover:luxury-text transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Collection Summary */}
          <div className="luxury-card rounded-none p-6">
            <h3 className="font-luxury text-xl luxury-text mb-6">Order Summary</h3>

            <div className="space-y-4 mb-6">
              <div className="flex justify-between font-elegant luxury-text">
                <span>Subtotal ({state.items.reduce((sum, item) => sum + item.quantity, 0)} items)</span>
                <span>${state.total.toFixed(2)}</span>
              </div>

              {state.discountAmount > 0 && (
                <div className="flex justify-between font-elegant luxury-accent">
                  <span>Discount ({state.discountCode})</span>
                  <span>-${state.discountAmount.toFixed(2)}</span>
                </div>
              )}

              <div className="flex justify-between font-elegant luxury-text">
                <span>Shipping</span>
                <span>Complimentary</span>
              </div>
            </div>

            <div className="border-t border-gray-100 pt-6">
              <div className="flex justify-between font-luxury text-2xl luxury-text">
                <span>Total</span>
                <span>${(state.total - state.discountAmount).toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>

      {/* Luxury Bottom Navigation - NEVER scrolls */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-100">
        <div className="max-w-sm mx-auto">
          <div className="flex items-center justify-between px-8 py-6">
            <div className="flex items-center space-x-3">
              <div className="w-1.5 h-1.5 bg-gray-300 rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-gray-300 rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-black rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-gray-300 rounded-full"></div>
            </div>
            <h2 className="font-luxury text-3xl luxury-text tracking-tight">Luxora</h2>
            <button
              onClick={handleCartClick}
              className="relative touch-target p-3 bg-black hover:bg-gray-800 active:bg-gray-900 rounded-full transition-all duration-300 shadow-elegant"
            >
              <ShoppingBag className="w-5 h-5 text-white" />
              {state.items.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-black text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold border-2 border-white">
                  {state.items.length}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;
