import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Plus, Minus, Trash2, ShoppingBag } from 'lucide-react';
import { useCart } from '../../contexts/CartContext';
import { useTelegram } from '../../hooks/useTelegram';

const CartPage: React.FC = () => {
  const navigate = useNavigate();
  const { state, dispatch } = useCart();
  const { hapticFeedback, mainButton } = useTelegram();

  const handleBack = () => {
    hapticFeedback.light();
    navigate(-1);
  };

  const handleUpdateQuantity = (productId: string, newQuantity: number) => {
    hapticFeedback.light();
    if (newQuantity <= 0) {
      dispatch({ type: 'REMOVE_ITEM', payload: productId });
    } else {
      dispatch({ 
        type: 'UPDATE_QUANTITY', 
        payload: { id: productId, quantity: newQuantity } 
      });
    }
  };

  const handleRemoveItem = (productId: string) => {
    hapticFeedback.medium();
    dispatch({ type: 'REMOVE_ITEM', payload: productId });
  };

  const handleCheckout = () => {
    hapticFeedback.medium();
    navigate('/checkout');
  };

  const handleCartClick = () => {
    // Already on cart page, do nothing or scroll to top
    hapticFeedback.light();
  };

  // Configure main button
  React.useEffect(() => {
    if (state.items.length > 0) {
      mainButton.setText(`Checkout • ETB ${state.total.toFixed(2)}`);
      mainButton.show();
      mainButton.onClick(handleCheckout);
    } else {
      mainButton.hide();
    }

    return () => {
      mainButton.offClick(handleCheckout);
    };
  }, [state.items.length, state.total, mainButton]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-green-100 to-green-200 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200 rounded-full opacity-30"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-green-300 rounded-full opacity-20"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-sm mx-auto bg-white/95 backdrop-blur-sm min-h-screen pb-24 mobile-scroll">
        {/* Header */}
        <div className="flex items-center justify-between p-6 pt-16">
          <button
            onClick={handleBack}
            className="touch-target p-3 hover:bg-gray-100 rounded-xl transition-colors"
          >
            <ArrowLeft className="w-6 h-6 text-gray-600" />
          </button>
          <h1 className="text-2xl font-bold text-gray-900 tracking-tight">Shopping Cart</h1>
          <div className="w-12"></div> {/* Spacer for centering */}
        </div>

      {state.items.length === 0 ? (
        /* Empty Cart */
        <div className="flex flex-col items-center justify-center min-h-[60vh] p-4">
          <div className="text-gray-400 mb-4">
            <ShoppingBag className="w-16 h-16 mx-auto" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Your cart is empty</h2>
          <p className="text-gray-500 text-center mb-6">
            Add some products to your cart to get started
          </p>
          <button
            onClick={() => navigate('/products')}
            className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
          >
            Start Shopping
          </button>
        </div>
      ) : (
        /* Cart Items */
        <div className="p-4">
          <div className="space-y-4 mb-6">
            {state.items.map((item) => (
              <div key={`${item.product.id}-${item.selectedColor}`} className="bg-white rounded-xl p-4 shadow-sm">
                <div className="flex items-start space-x-4">
                  {/* Product Image */}
                  <div className="flex-shrink-0">
                    <img
                      src={item.product.image}
                      alt={item.product.name}
                      className="w-16 h-16 object-cover rounded-lg"
                    />
                  </div>

                  {/* Product Info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 truncate">
                      {item.product.name}
                    </h3>
                    {item.selectedColor && (
                      <p className="text-sm text-gray-500">Color: {item.selectedColor}</p>
                    )}
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="font-bold text-gray-900">
                        ETB {item.product.price.toFixed(2)}
                      </span>
                      {item.product.originalPrice && (
                        <span className="text-sm text-gray-400 line-through">
                          ETB {item.product.originalPrice.toFixed(2)}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Remove Button */}
                  <button
                    onClick={() => handleRemoveItem(item.product.id)}
                    className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>

                {/* Quantity Controls */}
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => handleUpdateQuantity(item.product.id, item.quantity - 1)}
                      className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <span className="text-lg font-medium w-8 text-center">
                      {item.quantity}
                    </span>
                    <button
                      onClick={() => handleUpdateQuantity(item.product.id, item.quantity + 1)}
                      className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>

                  <div className="text-right">
                    <p className="font-bold text-gray-900">
                      ETB {(item.product.price * item.quantity).toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Cart Summary */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <h3 className="font-semibold text-gray-900 mb-3">Order Summary</h3>
            
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-gray-600">
                <span>Subtotal ({state.items.reduce((sum, item) => sum + item.quantity, 0)} items)</span>
                <span>ETB {state.total.toFixed(2)}</span>
              </div>
              
              {state.discountAmount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Discount ({state.discountCode})</span>
                  <span>-ETB {state.discountAmount.toFixed(2)}</span>
                </div>
              )}
              
              <div className="flex justify-between text-gray-600">
                <span>Shipping</span>
                <span>Free</span>
              </div>
            </div>

            <div className="border-t border-gray-200 pt-3">
              <div className="flex justify-between text-lg font-bold text-gray-900">
                <span>Total</span>
                <span>ETB {(state.total - state.discountAmount).toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Discount Code */}
          <div className="bg-white rounded-xl p-4 shadow-sm mt-4">
            <h3 className="font-semibold text-gray-900 mb-3">Discount Code</h3>
            <div className="flex space-x-2">
              <input
                type="text"
                placeholder="Enter discount code"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
              <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
      </div>

      {/* Fixed Bottom Navigation - NEVER scrolls */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white/98 backdrop-blur-xl border-t border-gray-100 safe-area-inset-bottom">
        <div className="max-w-sm mx-auto">
          <div className="flex items-center justify-between px-6 py-5">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-900 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 tracking-tight">Luxora</h2>
            <button
              onClick={handleCartClick}
              className="relative touch-target p-3 bg-pink-500 hover:bg-pink-600 active:bg-pink-700 rounded-2xl transition-colors shadow-lg"
            >
              <ShoppingBag className="w-6 h-6 text-white" />
              {state.items.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                  {state.items.length}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;
