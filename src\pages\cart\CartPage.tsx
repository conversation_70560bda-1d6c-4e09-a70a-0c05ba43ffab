import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Plus, Minus, Trash2, ShoppingBag } from 'lucide-react';
import { useCart } from '../../contexts/CartContext';
import { useTelegram } from '../../hooks/useTelegram';

const CartPage: React.FC = () => {
  const navigate = useNavigate();
  const { state, dispatch } = useCart();

  // Safe Telegram hook usage
  let hapticFeedback, mainButton;
  try {
    const telegram = useTelegram();
    hapticFeedback = telegram.hapticFeedback;
    mainButton = telegram.mainButton;
  } catch (error) {
    // Fallback for when Telegram is not available
    hapticFeedback = {
      light: () => {},
      medium: () => {},
      heavy: () => {},
      success: () => {}
    };
    mainButton = {
      setText: () => {},
      show: () => {},
      hide: () => {},
      onClick: () => {},
      offClick: () => {}
    };
  }

  const handleBack = () => {
    hapticFeedback.light();
    navigate(-1);
  };

  const handleUpdateQuantity = (productId: string, newQuantity: number) => {
    hapticFeedback.light();
    if (newQuantity <= 0) {
      dispatch({ type: 'REMOVE_ITEM', payload: productId });
    } else {
      dispatch({ 
        type: 'UPDATE_QUANTITY', 
        payload: { id: productId, quantity: newQuantity } 
      });
    }
  };

  const handleRemoveItem = (productId: string) => {
    hapticFeedback.medium();
    dispatch({ type: 'REMOVE_ITEM', payload: productId });
  };

  const handleCheckout = () => {
    hapticFeedback.medium();
    navigate('/checkout');
  };

  const handleCartClick = () => {
    // Already on cart page, do nothing or scroll to top
    hapticFeedback.light();
  };

  // Configure main button
  React.useEffect(() => {
    try {
      if (state.items.length > 0) {
        mainButton.setText(`Checkout • $${state.total.toFixed(2)}`);
        mainButton.show();
        mainButton.onClick(handleCheckout);
      } else {
        mainButton.hide();
      }

      return () => {
        mainButton.offClick(handleCheckout);
      };
    } catch (error) {
      console.log('Telegram main button not available');
    }
  }, [state.items.length, state.total, mainButton, handleCheckout]);

  return (
    <div className="min-h-screen bg-gray-50 relative">
      {/* Main Content */}
      <div className="relative z-10 max-w-sm mx-auto bg-white min-h-screen pb-24">
        {/* Modern Header */}
        <div className="px-4 pt-12 pb-6 bg-white border-b border-gray-200">
          <div className="flex items-center justify-between">
            <button
              onClick={handleBack}
              className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-700" />
            </button>
            <h1 className="font-display text-xl text-primary">Cart</h1>
            <div className="w-10"></div> {/* Spacer for centering */}
          </div>
        </div>

      {state.items.length === 0 ? (
        /* Empty Cart */
        <div className="flex flex-col items-center justify-center min-h-[60vh] px-6">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
            <ShoppingBag className="w-12 h-12 text-gray-400" />
          </div>
          <h2 className="font-display text-2xl text-primary mb-3">Your cart is empty</h2>
          <p className="font-text text-secondary text-center mb-8 leading-relaxed max-w-xs">
            Browse our products and add items to your cart to get started
          </p>
          <button
            onClick={() => navigate('/products')}
            className="btn-primary w-full max-w-xs"
          >
            Continue Shopping
          </button>
        </div>
      ) : (
        /* Cart Items */
        <div className="px-4 py-6">
          <div className="space-y-4 mb-6">
            {state.items.map((item) => (
              <div key={`${item.product.id}-${item.selectedColor}`} className="modern-card p-4">
                <div className="flex space-x-4">
                  {/* Product Image */}
                  <div className="w-20 h-20 flex-shrink-0">
                    <img
                      src={item.product.image}
                      alt={item.product.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  </div>

                  {/* Product Info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-text font-semibold text-primary text-lg mb-1 truncate">
                      {item.product.name}
                    </h3>
                    {item.selectedColor && (
                      <p className="font-caption text-secondary text-sm mb-2">Color: {item.selectedColor}</p>
                    )}
                    <div className="flex items-center justify-between">
                      <span className="font-text font-semibold text-primary text-lg">
                        ${item.product.price.toFixed(2)}
                      </span>
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() => handleUpdateQuantity(item.product.id, item.quantity - 1)}
                            className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
                          >
                            <Minus className="w-4 h-4 text-gray-600" />
                          </button>
                          <span className="font-text font-medium text-primary min-w-[24px] text-center">
                            {item.quantity}
                          </span>
                          <button
                            onClick={() => handleUpdateQuantity(item.product.id, item.quantity + 1)}
                            className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
                          >
                            <Plus className="w-4 h-4 text-gray-600" />
                          </button>
                        </div>

                        <button
                          onClick={() => handleRemoveItem(item.product.id)}
                          className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Order Summary */}
          <div className="modern-card p-6 mx-4">
            <h3 className="font-display text-lg text-primary mb-4">Order Summary</h3>

            <div className="space-y-3 mb-4">
              <div className="flex justify-between font-text text-primary">
                <span>Subtotal ({state.items.reduce((sum, item) => sum + item.quantity, 0)} items)</span>
                <span>${state.total.toFixed(2)}</span>
              </div>

              {state.discountAmount > 0 && (
                <div className="flex justify-between font-text text-green-600">
                  <span>Discount ({state.discountCode})</span>
                  <span>-${state.discountAmount.toFixed(2)}</span>
                </div>
              )}

              <div className="flex justify-between font-text text-primary">
                <span>Shipping</span>
                <span>Free</span>
              </div>
            </div>

            <div className="border-t border-gray-200 pt-4">
              <div className="flex justify-between font-display text-xl text-primary">
                <span>Total</span>
                <span>${(state.total - state.discountAmount).toFixed(2)}</span>
              </div>
            </div>

            {/* Checkout Button */}
            <button
              onClick={handleCheckout}
              className="btn-primary w-full mt-6"
            >
              Proceed to Checkout
            </button>
          </div>
        </div>
      )}
      </div>

      {/* Modern Bottom Navigation - NEVER scrolls */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200">
        <div className="max-w-sm mx-auto">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            </div>
            <h2 className="font-display text-2xl text-primary">Store</h2>
            <button
              onClick={handleCartClick}
              className="relative p-2 bg-blue-500 hover:bg-blue-600 rounded-full transition-colors"
            >
              <ShoppingBag className="w-5 h-5 text-white" />
              {state.items.length > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                  {state.items.length}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;
