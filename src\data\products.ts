import { Product, Deal, ProductCategory } from '../types/product';

export const products: Product[] = [
  {
    id: '1',
    name: 'Hyper',
    description: 'Futuristic design meets premium comfort. The Hyper features cutting-edge aesthetics with superior UV protection.',
    shortDescription: 'Futuristic sunglasses with premium comfort',
    price: 99.00,
    currency: 'ETB',
    image: 'https://images.pexels.com/photos/701877/pexels-photo-701877.jpeg?auto=compress&cs=tinysrgb&w=400',
    images: [
      'https://images.pexels.com/photos/701877/pexels-photo-701877.jpeg?auto=compress&cs=tinysrgb&w=400',
      'https://images.pexels.com/photos/46710/pexels-photo-46710.jpeg?auto=compress&cs=tinysrgb&w=400'
    ],
    category: 'sunglasses' as ProductCategory,
    brand: 'Luxora',
    sku: 'LUX-HYP-001',
    stock: 25,
    rating: 4.8,
    reviewCount: 1250,
    status: 'active',
    condition: 'new',
    isNew: false,
    isFeatured: true,
    specifications: {
      height: '4 cm',
      width: '15 cm',
      material: 'Titanium',
      frameType: 'Wraparound',
      lensType: 'Polarized',
      uvProtection: 'UV400',
      weight: '28g'
    },
    tags: ['premium', 'titanium', 'polarized', 'wraparound'],
    colors: ['Black', 'Silver', 'Gold'],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: 'Frame v1',
    description: 'Classic frame design with modern materials. Perfect for everyday wear with timeless appeal.',
    shortDescription: 'Classic everyday sunglasses',
    price: 82.00,
    originalPrice: 99.50,
    currency: 'ETB',
    image: 'https://images.pexels.com/photos/1031081/pexels-photo-1031081.jpeg?auto=compress&cs=tinysrgb&w=400',
    images: ['https://images.pexels.com/photos/1031081/pexels-photo-1031081.jpeg?auto=compress&cs=tinysrgb&w=400'],
    category: 'sunglasses' as ProductCategory,
    brand: 'Luxora',
    sku: 'LUX-FRM-001',
    stock: 18,
    rating: 4.6,
    reviewCount: 890,
    discount: 18,
    status: 'active',
    condition: 'new',
    isNew: false,
    isFeatured: false,
    specifications: {
      height: '4.5 cm',
      width: '14 cm',
      material: 'Acetate',
      frameType: 'Classic',
      lensType: 'UV400',
      weight: '32g'
    },
    tags: ['classic', 'acetate', 'everyday', 'affordable'],
    colors: ['Green', 'Brown', 'Black'],
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z'
  },
  {
    id: '3',
    name: 'Eclipse',
    description: 'Bold angular design that makes a statement. Eclipse combines style with exceptional lens technology.',
    shortDescription: 'Bold angular statement sunglasses',
    price: 70.00,
    originalPrice: 86.00,
    currency: 'ETB',
    image: 'https://images.pexels.com/photos/343720/pexels-photo-343720.jpeg?auto=compress&cs=tinysrgb&w=400',
    images: ['https://images.pexels.com/photos/343720/pexels-photo-343720.jpeg?auto=compress&cs=tinysrgb&w=400'],
    category: 'sunglasses' as ProductCategory,
    brand: 'Luxora',
    sku: 'LUX-ECL-001',
    stock: 12,
    rating: 4.7,
    reviewCount: 567,
    discount: 19,
    status: 'active',
    condition: 'new',
    isNew: false,
    isFeatured: false,
    specifications: {
      height: '4.2 cm',
      width: '15.5 cm',
      material: 'Carbon Fiber',
      frameType: 'Angular',
      lensType: 'Gradient',
      weight: '24g'
    },
    tags: ['angular', 'carbon-fiber', 'gradient', 'bold'],
    colors: ['Black', 'Matte Black', 'Gunmetal'],
    createdAt: '2024-01-08T10:00:00Z',
    updatedAt: '2024-01-08T10:00:00Z'
  },
  {
    id: '4',
    name: 'Spectra',
    description: 'Vibrant and colorful design that captures light beautifully. Spectra offers both style and protection.',
    shortDescription: 'Vibrant mirrored sunglasses',
    price: 54.50,
    currency: 'ETB',
    image: 'https://images.pexels.com/photos/1031080/pexels-photo-1031080.jpeg?auto=compress&cs=tinysrgb&w=400',
    images: ['https://images.pexels.com/photos/1031080/pexels-photo-1031080.jpeg?auto=compress&cs=tinysrgb&w=400'],
    category: 'sunglasses' as ProductCategory,
    brand: 'Luxora',
    sku: 'LUX-SPC-001',
    stock: 30,
    rating: 4.9,
    reviewCount: 2100,
    status: 'active',
    condition: 'new',
    isNew: true,
    isFeatured: true,
    specifications: {
      height: '4.8 cm',
      width: '14.5 cm',
      material: 'Polycarbonate',
      frameType: 'Round',
      lensType: 'Mirrored',
      weight: '26g'
    },
    tags: ['round', 'mirrored', 'colorful', 'lightweight'],
    colors: ['Gold', 'Rose Gold', 'Silver'],
    createdAt: '2024-01-20T10:00:00Z',
    updatedAt: '2024-01-20T10:00:00Z'
  },
  {
    id: '5',
    name: 'Eclipse V2',
    description: 'The evolution of our popular Eclipse model. Enhanced with premium materials and advanced lens technology.',
    shortDescription: 'Premium evolution of Eclipse',
    price: 165.00,
    originalPrice: 200.00,
    currency: 'ETB',
    image: 'https://images.pexels.com/photos/1031081/pexels-photo-1031081.jpeg?auto=compress&cs=tinysrgb&w=400',
    images: [
      'https://images.pexels.com/photos/1031081/pexels-photo-1031081.jpeg?auto=compress&cs=tinysrgb&w=400',
      'https://images.pexels.com/photos/701877/pexels-photo-701877.jpeg?auto=compress&cs=tinysrgb&w=400',
      'https://images.pexels.com/photos/343720/pexels-photo-343720.jpeg?auto=compress&cs=tinysrgb&w=400'
    ],
    category: 'sunglasses' as ProductCategory,
    brand: 'Luxora',
    sku: 'ER02542',
    stock: 8,
    rating: 4.9,
    reviewCount: 3400,
    discount: 18,
    status: 'active',
    condition: 'new',
    isNew: false,
    isFeatured: true,
    isSpecialOffer: true,
    specifications: {
      height: '4 cm',
      width: '15 cm',
      material: 'Glass',
      frameType: 'Aviator',
      lensType: 'Photochromic',
      weight: '35g'
    },
    tags: ['premium', 'aviator', 'photochromic', 'glass'],
    colors: ['Green', 'Blue', 'Clear'],
    createdAt: '2024-01-05T10:00:00Z',
    updatedAt: '2024-01-05T10:00:00Z'
  }
];

export const deals: Deal[] = [
  {
    id: '1',
    title: 'New Arrivals',
    description: 'Latest sunglasses collection with modern designs',
    products: products.slice(0, 4),
    discount: 15,
    discountType: 'percentage',
    startDate: '2024-01-01T00:00:00Z',
    endDate: '2024-12-31T23:59:59Z',
    isActive: true,
    currentUses: 0
  },
  {
    id: '2',
    title: 'Premium Collection',
    description: 'Exclusive premium sunglasses with advanced features',
    products: [products[4]],
    discount: 18,
    discountType: 'percentage',
    startDate: '2024-01-01T00:00:00Z',
    endDate: '2024-12-31T23:59:59Z',
    isActive: true,
    maxUses: 100,
    currentUses: 25
  }
];