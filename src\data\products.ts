import { Product, Deal } from '../types/product';

export const products: Product[] = [
  {
    id: '1',
    name: 'Hyper',
    price: 99.00,
    image: 'https://images.pexels.com/photos/701877/pexels-photo-701877.jpeg?auto=compress&cs=tinysrgb&w=400',
    images: [
      'https://images.pexels.com/photos/701877/pexels-photo-701877.jpeg?auto=compress&cs=tinysrgb&w=400',
      'https://images.pexels.com/photos/46710/pexels-photo-46710.jpeg?auto=compress&cs=tinysrgb&w=400'
    ],
    category: 'sunglasses',
    description: 'Futuristic design meets premium comfort. The Hyper features cutting-edge aesthetics with superior UV protection.',
    rating: 4.8,
    reviews: 1250,
    isFavorite: true,
    specifications: {
      height: '4 cm',
      width: '15 cm',
      material: 'Titanium',
      frameType: 'Wraparound',
      lensType: 'Polarized'
    },
    colors: ['Black', 'Silver', 'Gold']
  },
  {
    id: '2',
    name: 'Frame v1',
    price: 82.00,
    originalPrice: 99.50,
    image: 'https://images.pexels.com/photos/1031081/pexels-photo-1031081.jpeg?auto=compress&cs=tinysrgb&w=400',
    category: 'sunglasses',
    description: 'Classic frame design with modern materials. Perfect for everyday wear with timeless appeal.',
    rating: 4.6,
    reviews: 890,
    isFavorite: false,
    specifications: {
      height: '4.5 cm',
      width: '14 cm',
      material: 'Acetate',
      frameType: 'Classic',
      lensType: 'UV400'
    },
    colors: ['Green', 'Brown', 'Black']
  },
  {
    id: '3',
    name: 'Eclipse',
    price: 70.00,
    originalPrice: 86.00,
    image: 'https://images.pexels.com/photos/343720/pexels-photo-343720.jpeg?auto=compress&cs=tinysrgb&w=400',
    category: 'sunglasses',
    description: 'Bold angular design that makes a statement. Eclipse combines style with exceptional lens technology.',
    rating: 4.7,
    reviews: 567,
    isFavorite: false,
    specifications: {
      height: '4.2 cm',
      width: '15.5 cm',
      material: 'Carbon Fiber',
      frameType: 'Angular',
      lensType: 'Gradient'
    },
    colors: ['Black', 'Matte Black', 'Gunmetal']
  },
  {
    id: '4',
    name: 'Spectra',
    price: 54.50,
    image: 'https://images.pexels.com/photos/1031080/pexels-photo-1031080.jpeg?auto=compress&cs=tinysrgb&w=400',
    category: 'sunglasses',
    description: 'Vibrant and colorful design that captures light beautifully. Spectra offers both style and protection.',
    rating: 4.9,
    reviews: 2100,
    isFavorite: false,
    specifications: {
      height: '4.8 cm',
      width: '14.5 cm',
      material: 'Polycarbonate',
      frameType: 'Round',
      lensType: 'Mirrored'
    },
    colors: ['Gold', 'Rose Gold', 'Silver']
  },
  {
    id: '5',
    name: 'Eclipse V2',
    price: 165.00,
    originalPrice: 200.00,
    image: 'https://images.pexels.com/photos/1031081/pexels-photo-1031081.jpeg?auto=compress&cs=tinysrgb&w=400',
    images: [
      'https://images.pexels.com/photos/1031081/pexels-photo-1031081.jpeg?auto=compress&cs=tinysrgb&w=400',
      'https://images.pexels.com/photos/701877/pexels-photo-701877.jpeg?auto=compress&cs=tinysrgb&w=400',
      'https://images.pexels.com/photos/343720/pexels-photo-343720.jpeg?auto=compress&cs=tinysrgb&w=400'
    ],
    category: 'sunglasses',
    description: 'The evolution of our popular Eclipse model. Enhanced with premium materials and advanced lens technology.',
    rating: 4.9,
    reviews: 3400,
    sku: 'ER02542',
    specifications: {
      height: '4 cm',
      width: '15 cm',
      material: 'Glass',
      frameType: 'Aviator',
      lensType: 'Photochromic'
    },
    colors: ['Green', 'Blue', 'Clear']
  }
];

export const deals: Deal[] = [
  {
    id: '1',
    title: 'New Arrivals',
    products: products.slice(0, 4),
    discount: 15
  },
  {
    id: '2',
    title: 'Premium Collection',
    products: [products[4]],
    discount: 18
  }
];