// Extend the Telegram WebApp types for testing
import { TelegramWebApp } from '@twa-dev/types';

declare global {
  interface Window {
    Telegram?: {
      WebApp: TelegramWebApp.WebApp & {
        // Add our custom test methods
        triggerEvent: (eventName: string, ...args: any[]) => void;
      };
    };
  }
}

// This file doesn't export anything, it just augments the global scope
export {};
