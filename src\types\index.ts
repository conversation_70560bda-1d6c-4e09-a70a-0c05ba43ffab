// User-related types
export interface User {
  id: string;
  telegramId: string;
  firstName: string;
  lastName?: string;
  phone?: string;
  email?: string;
  role: 'customer' | 'vendor' | 'delivery' | 'admin';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Product-related types
export interface Product {
  id: string;
  vendorId: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  images: string[];
  category: string;
  stock: number;
  attributes: Record<string, any>;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Cart-related types
export interface CartItem {
  productId: string;
  quantity: number;
  price: number;
  productName: string;
  productImage?: string;
}

export interface Cart {
  items: CartItem[];
  total: number;
  currency: string;
}

// Order-related types
export type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'returned';

export interface OrderItem {
  productId: string;
  quantity: number;
  price: number;
  name: string;
  image?: string;
}

export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  status: OrderStatus;
  payment: {
    method: 'telebirr' | 'cod';
    status: 'pending' | 'completed' | 'failed' | 'refunded';
    amount: number;
    currency: string;
    transactionId?: string;
  };
  shipping: {
    address: Address;
    method: 'standard' | 'express';
    status: 'pending' | 'picked_up' | 'in_transit' | 'delivered';
    trackingNumber?: string;
    estimatedDelivery?: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Address type
export interface Address {
  id: string;
  userId: string;
  name: string;
  phone: string;
  landmark: string;
  details: string;
  isDefault: boolean;
  location?: {
    lat: number;
    lng: number;
  };
}

// API response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
  error?: string;
}

// Pagination types
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Language types
export type Language = 'en' | 'am';

// App context types
export interface AppContextType {
  theme: Theme;
  language: Language;
  user: User | null;
  cart: Cart;
  isAuthenticated: boolean;
  isLoading: boolean;
  setTheme: (theme: Theme) => void;
  setLanguage: (language: Language) => void;
  login: (userData: User) => void;
  logout: () => void;
  updateCart: (cart: Cart) => void;
  addToCart: (item: CartItem) => void;
  removeFromCart: (productId: string) => void;
  updateCartItem: (productId: string, quantity: number) => void;
}
