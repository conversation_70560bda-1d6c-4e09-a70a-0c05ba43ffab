import React from 'react';
import { ArrowLeft, Search, SlidersHorizontal } from 'lucide-react';
import { deals } from '../../data/products';
import ProductCard from '../Product/ProductCard';
import { Product } from '../../types/product';

interface DealsGridProps {
  onBack: () => void;
  onProductSelect: (product: Product) => void;
}

const DealsGrid: React.FC<DealsGridProps> = ({ onBack, onProductSelect }) => {
  return (
    <div className="min-h-screen bg-gray-50 pb-24">
      <div className="bg-white border-b border-gray-100 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={onBack}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-6 h-6 text-gray-700" />
            </button>
            <h1 className="text-lg font-semibold text-gray-900">Good deals</h1>
          </div>
        </div>
        <div className="mt-3 relative">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search..."
              className="w-full pl-10 pr-12 py-2.5 bg-gray-50 border border-gray-200 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
            <button className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-200 rounded-full transition-colors">
              <SlidersHorizontal className="w-4 h-4 text-gray-500" />
            </button>
          </div>
        </div>
      </div>

      <div className="p-4">
        {deals.map((deal) => (
          <div key={deal.id} className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-lg font-semibold text-gray-900">{deal.title}</h2>
              <button className="text-sm text-green-500 font-medium">See more</button>
            </div>
            <div className="grid grid-cols-2 gap-3">
              {deal.products.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  onSelect={onProductSelect}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DealsGrid;