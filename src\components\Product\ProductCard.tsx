import React from 'react';
import { Heart } from 'lucide-react';
import { Product } from '../../types/product';

interface ProductCardProps {
  product: Product;
  onSelect: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onSelect }) => {
  return (
    <div 
      className="cursor-pointer group"
      onClick={() => onSelect(product)}
    >
      {/* Product Name and Heart */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-gray-900 text-lg tracking-tight">{product.name}</h3>
        <button className="p-1 hover:bg-gray-100 rounded-lg transition-all duration-200">
          <Heart 
            className={`w-5 h-5 transition-all duration-200 ${
              product.isFavorite 
                ? 'text-pink-500 fill-current' 
                : 'text-gray-300 group-hover:text-pink-300'
            }`} 
          />
        </button>
      </div>

      {/* Product Image */}
      <div className="mb-4 relative overflow-hidden rounded-2xl bg-gray-100 group-hover:shadow-premium transition-all duration-300">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-36 object-cover transition-transform duration-300 group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      {/* Price */}
      <div className="flex items-center space-x-2">
        {product.originalPrice && (
          <span className="text-sm text-gray-400 line-through font-medium">
            ${product.originalPrice.toFixed(2)}
          </span>
        )}
        <span className="text-xl font-bold text-gray-900 tracking-tight">
          ${product.price.toFixed(2)}
        </span>
      </div>
    </div>
  );
};

export default ProductCard;