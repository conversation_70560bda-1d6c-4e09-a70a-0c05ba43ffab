import React, { useState, useRef, useEffect } from 'react';
import { Search, X, Clock, TrendingUp } from 'lucide-react';
import { useTelegram } from '../../hooks/useTelegram';

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: (query: string) => void;
  suggestions?: string[];
  recentSearches?: string[];
  placeholder?: string;
  showSuggestions?: boolean;
  className?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  onSearch,
  suggestions = [],
  recentSearches = [],
  placeholder = 'Search products...',
  showSuggestions = true,
  className = '',
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { hapticFeedback } = useTelegram();

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    setShowDropdown(showSuggestions && (newValue.length > 0 || recentSearches.length > 0));
  };

  const handleInputFocus = () => {
    setIsFocused(true);
    if (showSuggestions && (value.length > 0 || recentSearches.length > 0)) {
      setShowDropdown(true);
    }
    hapticFeedback.light();
  };

  const handleInputBlur = () => {
    setIsFocused(false);
    // Delay hiding dropdown to allow for suggestion clicks
    setTimeout(() => setShowDropdown(false), 150);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearch(value);
    }
  };

  const handleSearch = (query: string) => {
    if (query.trim()) {
      onSearch(query.trim());
      setShowDropdown(false);
      inputRef.current?.blur();
      hapticFeedback.medium();
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion);
    handleSearch(suggestion);
  };

  const handleClear = () => {
    onChange('');
    setShowDropdown(false);
    inputRef.current?.focus();
    hapticFeedback.light();
  };

  const displaySuggestions = value.length > 0 ? suggestions : [];
  const displayRecentSearches = value.length === 0 ? recentSearches : [];

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          className={`w-full pl-10 pr-10 py-3 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all ${
            isFocused ? 'border-green-300' : 'border-gray-300'
          }`}
        />
        {value && (
          <button
            onClick={handleClear}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-4 h-4 text-gray-400" />
          </button>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {showDropdown && showSuggestions && (displaySuggestions.length > 0 || displayRecentSearches.length > 0) && (
        <div
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto"
        >
          {/* Recent Searches */}
          {displayRecentSearches.length > 0 && (
            <div className="p-2">
              <div className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-500">
                <Clock className="w-4 h-4" />
                <span>Recent searches</span>
              </div>
              {displayRecentSearches.map((search, index) => (
                <button
                  key={`recent-${index}`}
                  onClick={() => handleSuggestionClick(search)}
                  className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded-lg transition-colors flex items-center space-x-2"
                >
                  <Clock className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-700">{search}</span>
                </button>
              ))}
            </div>
          )}

          {/* Suggestions */}
          {displaySuggestions.length > 0 && (
            <div className="p-2">
              {displayRecentSearches.length > 0 && (
                <div className="border-t border-gray-100 my-2" />
              )}
              <div className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-500">
                <TrendingUp className="w-4 h-4" />
                <span>Suggestions</span>
              </div>
              {displaySuggestions.map((suggestion, index) => (
                <button
                  key={`suggestion-${index}`}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded-lg transition-colors flex items-center space-x-2"
                >
                  <Search className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-700">
                    {suggestion.split(new RegExp(`(${value})`, 'gi')).map((part, i) =>
                      part.toLowerCase() === value.toLowerCase() ? (
                        <mark key={i} className="bg-yellow-200 text-gray-900">
                          {part}
                        </mark>
                      ) : (
                        part
                      )
                    )}
                  </span>
                </button>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
