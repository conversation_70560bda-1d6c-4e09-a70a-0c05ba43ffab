import React, { useEffect, useMemo } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Filter, Grid, List, X } from 'lucide-react';
import { useProductSearch } from '../hooks/useProductSearch';
import { useTelegram } from '../hooks/useTelegram';
import { SearchBar } from '../components/common/SearchBar';
import { FilterPanel } from '../components/common/FilterPanel';
import ProductCard from '../components/Product/ProductCard';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { Product } from '../types/product';
import { products } from '../data/products';

const SearchPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const { hapticFeedback } = useTelegram();
  
  const {
    query,
    results,
    isLoading,
    suggestions,
    recentSearches,
    filters,
    sortBy,
    totalResults,
    search,
    clearSearch,
    addFilter,
    removeFilter,
    clearFilters,
    setSortBy,
    addToRecentSearches,
    getSuggestions,
  } = useProductSearch();

  const [viewMode, setViewMode] = React.useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = React.useState(false);

  // Get available categories and brands for filters
  const availableCategories = useMemo(() => {
    const categoryCount: Record<string, number> = {};
    products.forEach(product => {
      categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;
    });
    return Object.entries(categoryCount).map(([category, count]) => ({
      category: category as any,
      count,
    }));
  }, []);

  const availableBrands = useMemo(() => {
    const brandCount: Record<string, number> = {};
    products.forEach(product => {
      if (product.brand) {
        brandCount[product.brand] = (brandCount[product.brand] || 0) + 1;
      }
    });
    return Object.entries(brandCount).map(([brand, count]) => ({
      brand,
      count,
    }));
  }, []);

  const priceRange = useMemo(() => {
    const prices = products.map(p => p.price);
    return {
      min: Math.min(...prices),
      max: Math.max(...prices),
    };
  }, []);

  // Initialize search from URL params
  useEffect(() => {
    const q = searchParams.get('q');
    const category = searchParams.get('category');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const sort = searchParams.get('sort');

    if (q) {
      search(q);
    }

    if (category) {
      addFilter({ categories: [category as any] });
    }

    if (minPrice && maxPrice) {
      addFilter({
        priceRange: {
          min: parseFloat(minPrice),
          max: parseFloat(maxPrice),
        },
      });
    }

    if (sort) {
      setSortBy(sort as any);
    }
  }, []);

  const handleBack = () => {
    hapticFeedback.light();
    navigate(-1);
  };

  const handleSearch = (searchQuery: string) => {
    search(searchQuery);
    addToRecentSearches(searchQuery);
    
    // Update URL
    const newParams = new URLSearchParams(searchParams);
    if (searchQuery) {
      newParams.set('q', searchQuery);
    } else {
      newParams.delete('q');
    }
    setSearchParams(newParams);
  };

  const handleProductSelect = (product: Product) => {
    hapticFeedback.medium();
    navigate(`/products/${product.id}`);
  };

  const handleFilterChange = (newFilters: any) => {
    // Clear existing filters and apply new ones
    clearFilters();
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        addFilter({ [key]: value });
      }
    });
  };

  const handleSortChange = (newSort: string) => {
    setSortBy(newSort as any);
    
    // Update URL
    const newParams = new URLSearchParams(searchParams);
    newParams.set('sort', newSort);
    setSearchParams(newParams);
  };

  const activeFiltersCount = Object.keys(filters).length;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="px-4 py-3">
          <div className="flex items-center space-x-3 mb-3">
            <button
              onClick={handleBack}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ArrowLeft className="w-6 h-6 text-gray-700" />
            </button>
            <h1 className="text-xl font-bold text-gray-900">Search</h1>
          </div>
          
          {/* Search Bar */}
          <SearchBar
            value={query}
            onChange={search}
            onSearch={handleSearch}
            suggestions={getSuggestions(query)}
            recentSearches={recentSearches}
            placeholder="Search products, brands, categories..."
            className="mb-3"
          />

          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => {
                  setShowFilters(!showFilters);
                  hapticFeedback.light();
                }}
                className="flex items-center space-x-1 px-3 py-2 bg-gray-100 rounded-lg relative"
              >
                <Filter className="w-4 h-4" />
                <span className="text-sm">Filters</span>
                {activeFiltersCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-green-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {activeFiltersCount}
                  </span>
                )}
              </button>

              <select
                value={sortBy}
                onChange={(e) => handleSortChange(e.target.value)}
                className="px-3 py-2 bg-gray-100 rounded-lg text-sm"
              >
                <option value="newest">Newest</option>
                <option value="oldest">Oldest</option>
                <option value="price-low-high">Price: Low to High</option>
                <option value="price-high-low">Price: High to Low</option>
                <option value="rating-high-low">Rating: High to Low</option>
                <option value="name-a-z">Name: A to Z</option>
                <option value="popularity">Most Popular</option>
              </select>
            </div>

            <div className="flex items-center space-x-1">
              <button
                onClick={() => {
                  setViewMode('grid');
                  hapticFeedback.light();
                }}
                className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => {
                  setViewMode('list');
                  hapticFeedback.light();
                }}
                className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Active Filters */}
      {activeFiltersCount > 0 && (
        <div className="bg-white border-b border-gray-100 px-4 py-2">
          <div className="flex items-center space-x-2 flex-wrap">
            <span className="text-sm text-gray-600">Active filters:</span>
            {Object.entries(filters).map(([key, value]) => (
              <button
                key={key}
                onClick={() => removeFilter(key as any)}
                className="flex items-center space-x-1 bg-green-100 text-green-700 px-2 py-1 rounded-lg text-sm"
              >
                <span>{key}: {Array.isArray(value) ? value.join(', ') : String(value)}</span>
                <X className="w-3 h-3" />
              </button>
            ))}
            <button
              onClick={clearFilters}
              className="text-sm text-red-600 hover:text-red-700"
            >
              Clear all
            </button>
          </div>
        </div>
      )}

      {/* Results Info */}
      <div className="px-4 py-3 bg-white border-b border-gray-100">
        <p className="text-sm text-gray-600">
          {query ? (
            <>
              {isLoading ? 'Searching...' : `${totalResults} results for "${query}"`}
            </>
          ) : (
            'Enter a search term to find products'
          )}
        </p>
      </div>

      {/* Results */}
      <div className="p-4">
        {isLoading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : results.length === 0 && query ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-2">
              <Filter className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-1">No results found</h3>
            <p className="text-gray-500 mb-4">
              Try adjusting your search terms or filters
            </p>
            <button
              onClick={() => {
                clearSearch();
                clearFilters();
              }}
              className="text-green-600 hover:text-green-700 font-medium"
            >
              Clear search and filters
            </button>
          </div>
        ) : results.length > 0 ? (
          <div className={`grid gap-4 ${viewMode === 'grid' ? 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4' : 'grid-cols-1'}`}>
            {results.map((product, index) => (
              <div
                key={product.id}
                className="animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <ProductCard
                  product={product}
                  onSelect={handleProductSelect}
                />
              </div>
            ))}
          </div>
        ) : !query && recentSearches.length > 0 ? (
          <div className="bg-white rounded-xl p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Recent Searches</h3>
            <div className="space-y-2">
              {recentSearches.map((recentSearch, index) => (
                <button
                  key={index}
                  onClick={() => handleSearch(recentSearch)}
                  className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  {recentSearch}
                </button>
              ))}
            </div>
          </div>
        ) : null}
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <FilterPanel
          filters={filters}
          onFiltersChange={handleFilterChange}
          onClose={() => setShowFilters(false)}
          availableCategories={availableCategories}
          availableBrands={availableBrands}
          priceRange={priceRange}
        />
      )}
    </div>
  );
};

export default SearchPage;
