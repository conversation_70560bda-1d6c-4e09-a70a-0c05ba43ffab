import React, { createContext, useContext, useEffect, useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useTelegram } from '@/hooks/useTelegram';
import { authService } from '@/services/auth.service';
import { APP_CONFIG } from '@/config/app';
import { User } from '@/types';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: { phone: string; password: string }) => Promise<void>;
  loginWithTelegram: () => Promise<void>;
  register: (userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const queryClient = useQueryClient();
  const { tg, isReady } = useTelegram();

  // For now, disable API calls and use mock data for development
  useEffect(() => {
    // Simulate loading and then set to not authenticated for development
    const timer = setTimeout(() => {
      setIsLoading(false);
      setUser(null); // Not authenticated by default
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Disable Telegram authentication for now (development mode)
  // useEffect(() => {
  //   if (isReady && tg?.initData) {
  //     const initData = tg.initData;
  //     const handleAuth = async () => {
  //       try {
  //         const { user, accessToken, refreshToken } = await authService.loginWithTelegram(initData);
  //         localStorage.setItem(APP_CONFIG.AUTH_TOKEN_KEY, accessToken);
  //         localStorage.setItem(APP_CONFIG.REFRESH_TOKEN_KEY, refreshToken);
  //         setUser(user);
  //       } catch (error) {
  //         console.error('Telegram auth failed:', error);
  //       }
  //     };
  //
  //     handleAuth();
  //   }
  // }, [isReady, tg]);

  const login = async (credentials: { phone: string; password: string }) => {
    // Mock login for development
    console.log('Mock login with:', credentials);
    const mockUser: User = {
      id: '1',
      telegramId: '123456789',
      firstName: 'John',
      lastName: 'Doe',
      phone: credentials.phone,
      email: '<EMAIL>',
      role: 'customer',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setUser(mockUser);
  };

  const loginWithTelegram = async () => {
    // Mock Telegram login for development
    console.log('Mock Telegram login');
    const mockUser: User = {
      id: '1',
      telegramId: '123456789',
      firstName: 'Telegram',
      lastName: 'User',
      role: 'customer',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setUser(mockUser);
  };

  const register = async (userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) => {
    // Mock registration for development
    console.log('Mock register with:', userData);
    const mockUser: User = {
      ...userData,
      id: '1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setUser(mockUser);
  };

  const logout = async () => {
    console.log('Mock logout');
    setUser(null);
    localStorage.removeItem(APP_CONFIG.AUTH_TOKEN_KEY);
    localStorage.removeItem(APP_CONFIG.REFRESH_TOKEN_KEY);
  };

  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    loginWithTelegram,
    register,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
