import React, { createContext, useContext, useEffect, useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useTelegram } from '@/hooks/useTelegram';
import { authService } from '@/services/auth.service';
import { APP_CONFIG } from '@/config/app';
import { User } from '@/types';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: { phone: string; password: string }) => Promise<void>;
  loginWithTelegram: () => Promise<void>;
  register: (userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const queryClient = useQueryClient();
  const { tg, isReady } = useTelegram();

  // Fetch current user on mount and when tokens change
  const { data: currentUser, isLoading: isUserLoading } = useQuery({
    queryKey: ['currentUser'],
    queryFn: authService.getCurrentUser,
    enabled: !!localStorage.getItem(APP_CONFIG.AUTH_TOKEN_KEY),
    retry: false,
  });

  // Update user state when currentUser changes
  useEffect(() => {
    if (currentUser) {
      setUser(currentUser);
    } else {
      setUser(null);
    }
    setIsLoading(isUserLoading);
  }, [currentUser, isUserLoading]);

  // Handle Telegram authentication
  useEffect(() => {
    if (isReady && tg?.initData) {
      const initData = tg.initData;
      const handleAuth = async () => {
        try {
          const { user, accessToken, refreshToken } = await authService.loginWithTelegram(initData);
          localStorage.setItem(APP_CONFIG.AUTH_TOKEN_KEY, accessToken);
          localStorage.setItem(APP_CONFIG.REFRESH_TOKEN_KEY, refreshToken);
          setUser(user);
        } catch (error) {
          console.error('Telegram auth failed:', error);
        }
      };
      
      handleAuth();
    }
  }, [isReady, tg]);

  const login = async (credentials: { phone: string; password: string }) => {
    const { user, accessToken, refreshToken } = await authService.login(credentials);
    localStorage.setItem(APP_CONFIG.AUTH_TOKEN_KEY, accessToken);
    localStorage.setItem(APP_CONFIG.REFRESH_TOKEN_KEY, refreshToken);
    setUser(user);
  };

  const loginWithTelegram = async () => {
    if (!tg) return;
    
    try {
      const initData = tg.initData;
      if (!initData) {
        tg.openTelegramLink(`https://t.me/${APP_CONFIG.TELEGRAM_BOT_NAME}?start=login`);
        return;
      }
      
      const { user, accessToken, refreshToken } = await authService.loginWithTelegram(initData);
      localStorage.setItem(APP_CONFIG.AUTH_TOKEN_KEY, accessToken);
      localStorage.setItem(APP_CONFIG.REFRESH_TOKEN_KEY, refreshToken);
      setUser(user);
    } catch (error) {
      console.error('Telegram login failed:', error);
      throw error;
    }
  };

  const register = async (userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) => {
    const { user, accessToken, refreshToken } = await authService.register(userData);
    localStorage.setItem(APP_CONFIG.AUTH_TOKEN_KEY, accessToken);
    localStorage.setItem(APP_CONFIG.REFRESH_TOKEN_KEY, refreshToken);
    setUser(user);
  };

  const logout = async () => {
    try {
      await authService.logout();
    } finally {
      setUser(null);
      queryClient.clear();
    }
  };

  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    loginWithTelegram,
    register,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
