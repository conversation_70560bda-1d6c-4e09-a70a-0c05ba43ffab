import { apiRequest } from '@/lib/api';
import { User } from '@/types';

export interface LoginCredentials {
  phone: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export const authService = {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiRequest<AuthResponse>({
      method: 'POST',
      url: '/auth/login',
      data: credentials,
    });
    return response.data;
  },

  async loginWithTelegram(initData: string): Promise<AuthResponse> {
    const response = await apiRequest<AuthResponse>({
      method: 'POST',
      url: '/auth/telegram',
      data: { initData },
    });
    return response.data;
  },

  async register(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<AuthResponse> {
    const response = await apiRequest<AuthResponse>({
      method: 'POST',
      url: '/auth/register',
      data: userData,
    });
    return response.data;
  },

  async logout(): Promise<void> {
    try {
      await apiRequest({
        method: 'POST',
        url: '/auth/logout',
      });
    } finally {
      // Clear tokens even if the request fails
      localStorage.removeItem(APP_CONFIG.AUTH_TOKEN_KEY);
      localStorage.removeItem(APP_CONFIG.REFRESH_TOKEN_KEY);
    }
  },

  async getCurrentUser(): Promise<User> {
    const response = await apiRequest<{ user: User }>({
      method: 'GET',
      url: '/auth/me',
    });
    return response.data.user;
  },

  async requestPasswordReset(email: string): Promise<void> {
    await apiRequest({
      method: 'POST',
      url: '/auth/forgot-password',
      data: { email },
    });
  },

  async resetPassword(token: string, password: string): Promise<void> {
    await apiRequest({
      method: 'POST',
      url: '/auth/reset-password',
      data: { token, password },
    });
  },

  async verifyEmail(token: string): Promise<void> {
    await apiRequest({
      method: 'POST',
      url: '/auth/verify-email',
      data: { token },
    });
  },
};
