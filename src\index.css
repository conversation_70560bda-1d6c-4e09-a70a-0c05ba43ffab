@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Apple-inspired Modern Color Palette */
    --primary-black: #1d1d1f;
    --primary-gray: #86868b;
    --secondary-gray: #f5f5f7;
    --tertiary-gray: #fbfbfd;
    --accent-blue: #007aff;
    --accent-green: #30d158;
    --accent-orange: #ff9500;
    --accent-red: #ff3b30;
    --background-primary: #ffffff;
    --background-secondary: #f2f2f7;
    --text-primary: #1d1d1f;
    --text-secondary: #86868b;
    --border-color: #d2d2d7;
    --card-background: #ffffff;

    /* Modern Shadows */
    --shadow-card: 0 4px 16px rgba(0, 0, 0, 0.1);
    --shadow-elevated: 0 8px 32px rgba(0, 0, 0, 0.12);
    --shadow-button: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  * {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Mobile optimizations */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  body {
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    letter-spacing: -0.011em;
    /* Mobile app optimizations */
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }

  #root {
    width: 100%;
    min-height: 100vh;
  }

  /* Make text selectable where needed */
  input, textarea {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}

@layer utilities {
  /* Modern Typography */
  .font-display {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
    letter-spacing: -0.022em;
  }

  .font-text {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    letter-spacing: -0.003em;
  }

  .font-caption {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    letter-spacing: 0.007em;
  }

  .text-balance {
    text-wrap: balance;
  }

  /* Modern Card Styles */
  .modern-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: var(--shadow-card);
    transition: all 0.2s ease-in-out;
  }

  .modern-card:hover {
    box-shadow: var(--shadow-elevated);
    transform: translateY(-1px);
  }

  /* Modern Button Styles */
  .btn-primary {
    background: var(--accent-blue);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 17px;
    line-height: 1.2;
    padding: 12px 24px;
    transition: all 0.2s ease-in-out;
  }

  .btn-primary:hover {
    background: #0056cc;
    transform: translateY(-1px);
    box-shadow: var(--shadow-button);
  }

  .btn-secondary {
    background: var(--secondary-gray);
    color: var(--text-primary);
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 17px;
    line-height: 1.2;
    padding: 12px 24px;
    transition: all 0.2s ease-in-out;
  }

  .btn-secondary:hover {
    background: #e5e5ea;
    transform: translateY(-1px);
  }

  /* Text Colors */
  .text-primary {
    color: var(--text-primary);
  }

  .text-secondary {
    color: var(--text-secondary);
  }

  .text-accent {
    color: var(--accent-blue);
  }

  /* Mobile Touch Optimizations */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Smooth Animations */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-right {
    animation: slideRight 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.4s ease-out;
  }

  /* Mobile-specific utilities */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .no-scroll {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  /* Native app feel */
  .app-container {
    max-width: 428px;
    margin: 0 auto;
    position: relative;
    min-height: 100vh;
    overflow-x: hidden;
  }
}

@keyframes slideRight {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}