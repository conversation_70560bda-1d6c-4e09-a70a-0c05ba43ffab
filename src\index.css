@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Mobile optimizations */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  body {
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    letter-spacing: -0.011em;
    /* Mobile app optimizations */
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }

  #root {
    width: 100%;
    min-height: 100vh;
  }

  /* Make text selectable where needed */
  input, textarea {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .font-display {
    font-feature-settings: 'ss01', 'ss02';
  }
  
  .glass-effect {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .shadow-premium {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  }
  
  .shadow-soft {
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
  }
  
  .animate-slide-right {
    animation: slideRight 0.3s ease-out;
  }

  /* Mobile-specific utilities */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .no-scroll {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  /* Native app feel */
  .app-container {
    max-width: 428px;
    margin: 0 auto;
    position: relative;
    min-height: 100vh;
    overflow-x: hidden;
  }
}

@keyframes slideRight {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}