@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Luxury Color Palette */
    --luxury-black: #000000;
    --luxury-charcoal: #1a1a1a;
    --luxury-dark-gray: #2d2d2d;
    --luxury-gray: #6b6b6b;
    --luxury-light-gray: #f5f5f5;
    --luxury-cream: #faf9f7;
    --luxury-white: #ffffff;
    --luxury-gold: #d4af37;
    --luxury-rose-gold: #e8b4a0;
    --luxury-champagne: #f7e7ce;
    --luxury-accent: #8b5a3c;

    /* Luxury Shadows */
    --shadow-luxury: 0 8px 32px rgba(0, 0, 0, 0.12);
    --shadow-elegant: 0 4px 20px rgba(0, 0, 0, 0.08);
    --shadow-subtle: 0 2px 12px rgba(0, 0, 0, 0.04);
  }

  * {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Mobile optimizations */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  body {
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    letter-spacing: -0.011em;
    /* Mobile app optimizations */
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }

  #root {
    width: 100%;
    min-height: 100vh;
  }

  /* Make text selectable where needed */
  input, textarea {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}

@layer utilities {
  /* Luxury Typography */
  .font-luxury {
    font-family: 'Playfair Display', serif;
    font-weight: 400;
    letter-spacing: -0.02em;
  }

  .font-luxury-bold {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    letter-spacing: -0.02em;
  }

  .font-elegant {
    font-family: 'Inter', sans-serif;
    font-weight: 300;
    letter-spacing: 0.02em;
  }

  .text-balance {
    text-wrap: balance;
  }

  /* Luxury Effects */
  .luxury-glass {
    backdrop-filter: blur(24px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .luxury-card {
    background: var(--luxury-white);
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: var(--shadow-elegant);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .luxury-card:hover {
    box-shadow: var(--shadow-luxury);
    transform: translateY(-2px);
  }

  .luxury-button {
    background: var(--luxury-black);
    color: var(--luxury-white);
    border: none;
    font-weight: 500;
    letter-spacing: 0.025em;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .luxury-button:hover {
    background: var(--luxury-charcoal);
    transform: translateY(-1px);
    box-shadow: var(--shadow-elegant);
  }

  .luxury-accent {
    color: var(--luxury-gold);
  }

  .luxury-text {
    color: var(--luxury-charcoal);
  }

  .luxury-text-light {
    color: var(--luxury-gray);
  }
  
  .animate-slide-right {
    animation: slideRight 0.3s ease-out;
  }

  /* Mobile-specific utilities */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .no-scroll {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  /* Native app feel */
  .app-container {
    max-width: 428px;
    margin: 0 auto;
    position: relative;
    min-height: 100vh;
    overflow-x: hidden;
  }
}

@keyframes slideRight {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}