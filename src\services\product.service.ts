import { apiRequest } from '../lib/api';
import { 
  Product, 
  ProductFilter, 
  ProductSortOption, 
  ProductSearchResult,
  ProductReview,
  Deal
} from '../types/product';

export interface GetProductsParams {
  page?: number;
  limit?: number;
  search?: string;
  filter?: ProductFilter;
  sort?: ProductSortOption;
}

export interface GetProductsResponse {
  products: Product[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

class ProductService {
  // Get all products with filtering, sorting, and pagination
  async getProducts(params: GetProductsParams = {}): Promise<GetProductsResponse> {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.search) queryParams.append('search', params.search);
    if (params.sort) queryParams.append('sort', params.sort);
    
    // Add filter parameters
    if (params.filter) {
      if (params.filter.categories?.length) {
        queryParams.append('categories', params.filter.categories.join(','));
      }
      if (params.filter.priceRange) {
        queryParams.append('minPrice', params.filter.priceRange.min.toString());
        queryParams.append('maxPrice', params.filter.priceRange.max.toString());
      }
      if (params.filter.rating) {
        queryParams.append('minRating', params.filter.rating.toString());
      }
      if (params.filter.brands?.length) {
        queryParams.append('brands', params.filter.brands.join(','));
      }
      if (params.filter.inStock !== undefined) {
        queryParams.append('inStock', params.filter.inStock.toString());
      }
      if (params.filter.onSale !== undefined) {
        queryParams.append('onSale', params.filter.onSale.toString());
      }
      if (params.filter.isNew !== undefined) {
        queryParams.append('isNew', params.filter.isNew.toString());
      }
      if (params.filter.isFeatured !== undefined) {
        queryParams.append('isFeatured', params.filter.isFeatured.toString());
      }
    }

    const response = await apiRequest<GetProductsResponse>({
      method: 'GET',
      url: `/products?${queryParams.toString()}`,
    });

    return response.data;
  }

  // Get a single product by ID
  async getProduct(id: string): Promise<Product> {
    const response = await apiRequest<Product>({
      method: 'GET',
      url: `/products/${id}`,
    });

    return response.data;
  }

  // Search products with advanced filtering
  async searchProducts(query: string, params: GetProductsParams = {}): Promise<ProductSearchResult> {
    const queryParams = new URLSearchParams();
    queryParams.append('q', query);
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sort) queryParams.append('sort', params.sort);
    
    if (params.filter) {
      if (params.filter.categories?.length) {
        queryParams.append('categories', params.filter.categories.join(','));
      }
      if (params.filter.priceRange) {
        queryParams.append('minPrice', params.filter.priceRange.min.toString());
        queryParams.append('maxPrice', params.filter.priceRange.max.toString());
      }
      if (params.filter.rating) {
        queryParams.append('minRating', params.filter.rating.toString());
      }
    }

    const response = await apiRequest<ProductSearchResult>({
      method: 'GET',
      url: `/products/search?${queryParams.toString()}`,
    });

    return response.data;
  }

  // Get featured products
  async getFeaturedProducts(limit: number = 10): Promise<Product[]> {
    const response = await apiRequest<Product[]>({
      method: 'GET',
      url: `/products/featured?limit=${limit}`,
    });

    return response.data;
  }

  // Get new arrivals
  async getNewArrivals(limit: number = 10): Promise<Product[]> {
    const response = await apiRequest<Product[]>({
      method: 'GET',
      url: `/products/new-arrivals?limit=${limit}`,
    });

    return response.data;
  }

  // Get products by category
  async getProductsByCategory(category: string, params: GetProductsParams = {}): Promise<GetProductsResponse> {
    const queryParams = new URLSearchParams();
    queryParams.append('category', category);
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sort) queryParams.append('sort', params.sort);

    const response = await apiRequest<GetProductsResponse>({
      method: 'GET',
      url: `/products/category?${queryParams.toString()}`,
    });

    return response.data;
  }

  // Get related products
  async getRelatedProducts(productId: string, limit: number = 6): Promise<Product[]> {
    const response = await apiRequest<Product[]>({
      method: 'GET',
      url: `/products/${productId}/related?limit=${limit}`,
    });

    return response.data;
  }

  // Get product reviews
  async getProductReviews(productId: string, page: number = 1, limit: number = 10): Promise<{
    reviews: ProductReview[];
    total: number;
    averageRating: number;
    ratingDistribution: Record<number, number>;
  }> {
    const response = await apiRequest<{
      reviews: ProductReview[];
      total: number;
      averageRating: number;
      ratingDistribution: Record<number, number>;
    }>({
      method: 'GET',
      url: `/products/${productId}/reviews?page=${page}&limit=${limit}`,
    });

    return response.data;
  }

  // Get current deals
  async getDeals(): Promise<Deal[]> {
    const response = await apiRequest<Deal[]>({
      method: 'GET',
      url: '/deals',
    });

    return response.data;
  }

  // Get product categories with counts
  async getCategories(): Promise<Array<{ category: string; count: number; subcategories?: Array<{ name: string; count: number }> }>> {
    const response = await apiRequest<Array<{ category: string; count: number; subcategories?: Array<{ name: string; count: number }> }>>({
      method: 'GET',
      url: '/products/categories',
    });

    return response.data;
  }

  // Get product brands with counts
  async getBrands(): Promise<Array<{ brand: string; count: number }>> {
    const response = await apiRequest<Array<{ brand: string; count: number }>>({
      method: 'GET',
      url: '/products/brands',
    });

    return response.data;
  }

  // Add product to wishlist
  async addToWishlist(productId: string): Promise<void> {
    await apiRequest({
      method: 'POST',
      url: `/wishlist/${productId}`,
    });
  }

  // Remove product from wishlist
  async removeFromWishlist(productId: string): Promise<void> {
    await apiRequest({
      method: 'DELETE',
      url: `/wishlist/${productId}`,
    });
  }

  // Get user's wishlist
  async getWishlist(): Promise<Product[]> {
    const response = await apiRequest<Product[]>({
      method: 'GET',
      url: '/wishlist',
    });

    return response.data;
  }
}

export const productService = new ProductService();
export default productService;
