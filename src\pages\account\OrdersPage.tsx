import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Package } from 'lucide-react';
import { useTelegram } from '../../hooks/useTelegram';

const OrdersPage: React.FC = () => {
  const navigate = useNavigate();
  const { hapticFeedback } = useTelegram();

  const handleBack = () => {
    hapticFeedback.light();
    navigate(-1);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center space-x-3">
          <button
            onClick={handleBack}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <ArrowLeft className="w-6 h-6 text-gray-700" />
          </button>
          <h1 className="text-xl font-bold text-gray-900">Orders</h1>
        </div>
      </div>

      <div className="p-4">
        <div className="bg-white rounded-xl p-6 text-center">
          <Package className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Orders Coming Soon</h2>
          <p className="text-gray-600">
            Order management will be implemented in the next phase.
          </p>
        </div>
      </div>
    </div>
  );
};

export default OrdersPage;
