# Souq - System Architecture

## 1. High-Level Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                      Telegram Client                            │
│  ┌─────────────┐    ┌───────────────────────────────────────┐  │
│  │  Mini App   │    │               Bot                     │  │
│  │  (React)    │◄──►│  (Node.js + Telegraf)                │  │
│  └─────────────┘    └───────────────────┬───────────────────┘  │
└─────────────────────────────────────────┼───────────────────────┘
                                          │
┌─────────────────────────────────────────▼───────────────────────┐
│                       Backend API                               │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                  API Gateway (REST/WebSocket)          │   │
│  └───────────┬───────────┬─────────────┬───────────────────┘   │
│              │           │             │                       │
│  ┌───────────▼───┐ ┌─────▼───────┐ ┌──▼─────────────────┐     │
│  │  Auth Service │ │ Order Service│ │  Payment Service  │     │
│  └───────────────┘ └──────────────┘ └───────────────────┘     │
│                                                               │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  Product Service│ │  User Service   │ │  Logistics     │ │
│  │                 │ │                 │ │    Service     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│                                                               │
└───────────────────────────────────────────────────────────────┘
```

## 2. Frontend Architecture

### 2.1 Core Technologies
- **Framework**: React 18 with TypeScript
- **State Management**: React Query + Context API
- **Styling**: Tailwind CSS with CSS Modules
- **Routing**: React Router DOM
- **Form Handling**: React Hook Form
- **Internationalization**: i18next

### 2.2 Module Structure
```
src/
├── assets/           # Static assets
├── components/       # Reusable UI components
│   ├── common/       # Generic components (buttons, inputs, etc.)
│   ├── layout/       # Layout components
│   └── ui/           # UI kit components
├── features/         # Feature-based modules
│   ├── auth/         # Authentication
│   ├── products/     # Product catalog
│   ├── cart/         # Shopping cart
│   ├── checkout/     # Checkout process
│   └── account/      # User account
├── hooks/            # Custom hooks
├── lib/              # Third-party library configurations
├── services/         # API service layer
├── stores/           # State management
├── types/            # TypeScript type definitions
└── utils/            # Utility functions
```

## 3. Backend Architecture

### 3.1 Core Technologies
- **Runtime**: Node.js 18+
- **Framework**: NestJS
- **API**: REST + WebSocket
- **Database**: MongoDB (primary), Redis (caching)
- **Search**: Elasticsearch
- **Message Queue**: RabbitMQ

### 3.2 Service Breakdown

#### 3.2.1 Auth Service
- User authentication & authorization
- Session management
- JWT token generation/validation
- Social login integration

#### 3.2.2 Product Service
- Product catalog management
- Inventory tracking
- Search functionality
- Categories and filters

#### 3.2.3 Order Service
- Order processing
- Order history
- Status tracking
- Returns/refunds

#### 3.2.4 Payment Service
- Telebirr integration
- Payment processing
- Escrow management
- Transaction history

#### 3.2.5 User Service
- User profiles
- Address management
- Preferences
- Notifications

#### 3.2.6 Logistics Service
- Delivery zone management
- Agent assignment
- Real-time tracking
- Delivery estimates

## 4. Data Models

### 4.1 User
```typescript
interface User {
  _id: string;
  telegramId: string;
  firstName: string;
  lastName?: string;
  phone?: string;
  email?: string;
  role: 'customer' | 'vendor' | 'delivery' | 'admin';
  addresses: Address[];
  createdAt: Date;
  updatedAt: Date;
}
```

### 4.2 Product
```typescript
interface Product {
  _id: string;
  vendorId: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  images: string[];
  category: string;
  stock: number;
  attributes: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### 4.3 Order
```typescript
interface Order {
  _id: string;
  userId: string;
  items: {
    productId: string;
    quantity: number;
    price: number;
  }[];
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  payment: {
    method: 'telebirr' | 'cod';
    status: 'pending' | 'completed' | 'failed' | 'refunded';
    amount: number;
    currency: string;
    transactionId?: string;
  };
  shipping: {
    address: Address;
    method: 'standard' | 'express';
    status: 'pending' | 'picked_up' | 'in_transit' | 'delivered';
    trackingNumber?: string;
    estimatedDelivery?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}
```

## 5. API Endpoints (Key Examples)

### 5.1 Auth
- `POST /auth/telegram` - Authenticate via Telegram
- `POST /auth/refresh` - Refresh access token
- `GET /auth/me` - Get current user profile

### 5.2 Products
- `GET /products` - List products with filters
- `GET /products/:id` - Get product details
- `POST /products` - Create product (vendor)
- `PUT /products/:id` - Update product (vendor)

### 5.3 Orders
- `POST /orders` - Create new order
- `GET /orders` - List user's orders
- `GET /orders/:id` - Get order details
- `PATCH /orders/:id/status` - Update order status

## 6. Deployment Architecture

### 6.1 Development
- Local development with Docker Compose
- Hot-reload for both frontend and backend
- MongoDB Atlas for database

### 6.2 Staging/Production
- Containerized with Docker
- Orchestrated with Kubernetes
- CI/CD with GitHub Actions
- Monitoring with Prometheus/Grafana
- Logging with ELK Stack

## 7. Security Considerations

### 7.1 Authentication & Authorization
- JWT-based authentication
- Role-based access control (RBAC)
- Rate limiting
- CORS policies

### 7.2 Data Protection
- Data encryption at rest and in transit
- PII protection
- Regular security audits

### 7.3 API Security
- Input validation
- SQL/NoSQL injection prevention
- CSRF protection
- Request sanitization

## 8. Performance Optimization

### 8.1 Frontend
- Code splitting
- Lazy loading
- Image optimization
- Caching strategies

### 8.2 Backend
- Database indexing
- Query optimization
- Caching with Redis
- Connection pooling

## 9. Monitoring & Logging

### 9.1 Monitoring
- Application metrics
- Error tracking
- Performance monitoring
- Uptime monitoring

### 9.2 Logging
- Structured logging
- Log levels
- Centralized log management
- Log rotation

## 10. Future Considerations

### 10.1 Scalability
- Horizontal scaling
- Database sharding
- Microservices decomposition

### 10.2 Features
- Push notifications
- Advanced analytics
- AI/ML recommendations
- Multi-vendor marketplace

### 10.3 Integrations
- SMS notifications
- Email service
- Social media sharing
- Third-party logistics providers
