import React, { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useTelegram } from '@/hooks/useTelegram';

interface ProtectedRouteProps {
  children: ReactNode;
  roles?: string[];
}

export const ProtectedRoute = ({ children, roles = [] }: ProtectedRouteProps) => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const { isReady } = useTelegram();
  const location = useLocation();

  // If we're still loading, show a loading spinner
  if (isLoading || !isReady) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // If not authenticated, redirect to login with the return url
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check if user has required roles
  if (roles.length > 0 && user?.role && !roles.includes(user.role)) {
    // User doesn't have required role, redirect to home or show unauthorized
    return <Navigate to="/unauthorized" state={{ from: location }} replace />;
  }

  // Authorized, render the children
  return <>{children}</>;
};

// Higher Order Component for protecting routes
export const withProtectedRoute = <P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ProtectedRouteProps, 'children'> = {}
) => {
  const WrappedComponent: React.FC<P> = (props) => (
    <ProtectedRoute {...options}>
      <Component {...props} />
    </ProtectedRoute>
  );
  
  return WrappedComponent;
};
