import { renderHook, act } from '@testing-library/react';
import { useTelegram } from '../useTelegram';

type ThemeParams = {
  bg_color?: string;
  text_color?: string;
  hint_color?: string;
  link_color?: string;
  button_color?: string;
  button_text_color?: string;
  secondary_bg_color?: string;
  header_bg_color?: string;
  accent_text_color?: string;
  section_bg_color?: string;
  section_header_text_color?: string;
  subtitle_text_color?: string;
  destructive_text_color?: string;
};

interface HapticFeedback {
  impactOccurred: (style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft') => void;
  notificationOccurred: (type: 'error' | 'success' | 'warning') => void;
  selectionChanged: () => void;
}

interface BackButton {
  show: () => void;
  hide: () => void;
  onClick: (callback: () => void) => void;
  offClick: (callback: () => void) => void;
  showProgress: (show: boolean) => void;
}

interface MainButton {
  text: string;
  color: string;
  textColor: string;
  isVisible: boolean;
  isActive: boolean;
  isProgressVisible: boolean;
  setText: (text: string) => void;
  onClick: (callback: () => void) => void;
  offClick: (callback: () => void) => void;
  show: () => void;
  hide: () => void;
  enable: () => void;
  disable: () => void;
  showProgress: (leaveActive?: boolean) => void;
  hideProgress: () => void;
  setParams: (params: {
    color?: string;
    text_color?: string;
    is_active?: boolean;
    is_visible?: boolean;
    text?: string;
  }) => void;
}

interface PopupButton {
  id?: string;
  type?: 'default' | 'ok' | 'close' | 'cancel' | 'destructive';
  text: string;
}

interface PopupParams {
  title?: string;
  message: string;
  buttons?: PopupButton[];
}

interface TelegramWebApp {
  initData: string;
  initDataUnsafe: {
    user?: {
      id: number;
      first_name: string;
      last_name?: string;
      username?: string;
      language_code?: string;
      is_premium?: boolean;
      photo_url?: string;
      allows_write_to_pm?: boolean;
    };
    chat_type?: 'sender' | 'private' | 'group' | 'supergroup' | 'channel';
    chat_instance?: string;
    start_param?: string;
    can_send_after?: number;
    auth_date: number;
    hash: string;
  };
  version: string;
  platform: string;
  colorScheme: 'light' | 'dark';
  themeParams: ThemeParams;
  isExpanded: boolean;
  viewportHeight: number;
  viewportStableHeight: number;
  headerColor: string;
  backgroundColor: string;
  isClosingConfirmationEnabled: boolean;
  BackButton: BackButton;
  MainButton: MainButton;
  HapticFeedback: HapticFeedback;
  ready: () => void;
  expand: () => void;
  close: () => void;
  onEvent: (eventType: string, eventHandler: Function) => void;
  offEvent: (eventType: string, eventHandler: Function) => void;
  sendData: (data: any) => void;
  showAlert: (message: string, callback?: () => void) => void;
  showPopup: (params: PopupParams, callback?: (buttonId: string) => void) => void;
  showConfirm: (message: string, callback?: (confirmed: boolean) => void) => void;
  openLink: (url: string, options?: { try_instant_view?: boolean }) => void;
  openTelegramLink: (url: string) => void;
  openInvoice: (url: string, callback?: (status: 'paid' | 'cancelled' | 'failed' | 'pending') => void) => void;
  switchInlineQuery: (query: string, choose_chat_types?: Array<'users' | 'bots' | 'groups' | 'channels'>) => void;
  showScanQrPopup: (params: { text?: string }, callback: (text: string) => void) => void;
  closeScanQrPopup: () => void;
  readTextFromClipboard: (callback: (text: string) => void) => void;
  requestWriteAccess: (callback?: (accessGranted: boolean) => void) => void;
  requestContact: (callback: (phoneNumber: string) => void) => void;
  setHeaderColor: (color: string) => void;
  setBackgroundColor: (color: string) => void;
  enableClosingConfirmation: () => void;
  disableClosingConfirmation: () => void;
  isVersionAtLeast: (version: string) => boolean;
}

// Extend the WebApp type with our test-specific methods
interface TestWebApp extends TelegramWebApp {
  triggerEvent: (eventName: string, ...args: any[]) => void;
}

interface MockTelegram {
  WebApp: TestWebApp;
}

// Mock the Telegram WebApp object with proper types
const createMockTelegram = (): MockTelegram => {
  const mockOnEvent = jest.fn();
  const mockOffEvent = jest.fn();
  
  // Create a mock WebApp object that matches the TelegramWebApp interface
  const mockWebApp: TestWebApp = {
    initData: 'test_init_data',
    initDataUnsafe: { 
      user: { 
        id: 12345, 
        first_name: 'Test',
        last_name: 'User',
        username: 'testuser',
        language_code: 'en',
        is_premium: true,
        photo_url: 'https://example.com/photo.jpg',
        allows_write_to_pm: true
      },
      chat_type: 'private',
      chat_instance: 'chat123',
      start_param: '',
      auth_date: Math.floor(Date.now() / 1000),
      hash: 'test_hash',
      can_send_after: 0
    },
    themeParams: { 
      bg_color: '#ffffff',
      text_color: '#000000',
      hint_color: '#999999',
      link_color: '#168acd',
      button_color: '#2481cc',
      button_text_color: '#ffffff',
      secondary_bg_color: '#f4f4f5'
    },
    version: '6.0',
    platform: 'tdesktop',
    colorScheme: 'light',
    isExpanded: true,
    viewportHeight: 800,
    viewportStableHeight: 700,
    headerColor: '#ffffff',
    backgroundColor: '#ffffff',
    isClosingConfirmationEnabled: false,
    isVersionAtLeast: jest.fn().mockImplementation((version) => {
      // Simple version comparison for testing
      const [major, minor] = version.split('.').map(Number);
      const [currentMajor, currentMinor] = '6.0'.split('.').map(Number);
      return currentMajor > major || (currentMajor === major && currentMinor >= minor);
    }),
    BackButton: {
      show: jest.fn(),
      hide: jest.fn(),
      onClick: (callback: () => void) => {
        // Store callback for testing
        const mockHandler = () => callback();
        mockOnEvent('backButtonClicked', mockHandler);
        return {
          off: () => mockOffEvent('backButtonClicked', mockHandler)
        };
      },
      offClick: (callback: () => void) => {
        // Remove callback
        mockOffEvent('backButtonClicked', callback);
        return this;
      },
      showProgress: jest.fn()
    },
    MainButton: {
      text: '',
      color: '',
      textColor: '',
      isVisible: false,
      isActive: false,
      isProgressVisible: false,
      setText: jest.fn(),
      onClick: (callback: () => void) => {
        // Store callback for testing
        return this;
      },
      offClick: (callback: () => void) => {
        // Remove callback
        return this;
      },
      show: jest.fn(),
      hide: jest.fn(),
      enable: jest.fn(),
      disable: jest.fn(),
      showProgress: jest.fn(),
      hideProgress: jest.fn(),
      setParams: jest.fn()
    },
    HapticFeedback: {
      impactOccurred: jest.fn(),
      notificationOccurred: jest.fn(),
      selectionChanged: jest.fn()
    },
    ready: jest.fn(),
    expand: jest.fn(),
    close: jest.fn(),
    onEvent: mockOnEvent,
    offEvent: mockOffEvent,
    sendData: jest.fn(),
    showAlert: jest.fn(),
    showPopup: jest.fn(),
    showConfirm: jest.fn().mockImplementation((message, callback) => {
      callback?.(true);
      return Promise.resolve(true);
    }),
    openLink: jest.fn(),
    openTelegramLink: jest.fn(),
    openInvoice: jest.fn(),
    switchInlineQuery: jest.fn(),
    showScanQrPopup: jest.fn(),
    closeScanQrPopup: jest.fn(),
    readTextFromClipboard: jest.fn().mockResolvedValue(''),
    requestWriteAccess: jest.fn(),
    requestContact: jest.fn(),
    setHeaderColor: jest.fn(),
    setBackgroundColor: jest.fn(),
    enableClosingConfirmation: jest.fn(),
    disableClosingConfirmation: jest.fn(),
    // Custom test helper method
    triggerEvent: (eventName: string, ...args: any[]) => {
      const call = mockOnEvent.mock.calls.find(([name]) => name === eventName);
      if (call && call[1]) {
        call[1](...args);
      }
    }
  };

  return { WebApp: mockWebApp };
};

// Import our type declarations
import '../../types/telegram-webapp-mock.d.ts';

describe('useTelegram', () => {
  let mockTelegram: MockTelegram;
  let originalTelegram: { WebApp: TestWebApp; } | undefined;
  
  beforeEach(() => {
    // Create a fresh mock for each test
    mockTelegram = createMockTelegram();
    
    // Reset all mocks before each test
    jest.clearAllMocks();
    
    // Store the original Telegram object
    originalTelegram = window.Telegram as { WebApp: TestWebApp; };
    
    // Set the global Telegram object
    window.Telegram = mockTelegram;
  });

  afterEach(() => {
    // Restore the original Telegram object
    window.Telegram = originalTelegram;
  });

  it('should initialize with default values when Telegram is not available', () => {
    // Remove the Telegram object for this test
    const originalTelegram = window.Telegram;
    // @ts-ignore - We're testing the case where Telegram is not available
    delete window.Telegram;
    
    const { result } = renderHook(() => useTelegram());
    
    expect(result.current.tg).toBeNull();
    expect(result.current.user).toBeNull();
    expect(result.current.theme).toBe('light');
    expect(result.current.isExpanded).toBe(false);
    expect(result.current.viewportHeight).toBe(0);
    
    // Restore the Telegram object
    window.Telegram = originalTelegram;
  });

  it('should initialize with Telegram WebApp values when available', () => {
    const { result } = renderHook(() => useTelegram());

    expect(result.current.tg).toBe(window.Telegram?.WebApp);
    expect(result.current.user).toEqual({
      id: 12345,
      first_name: 'Test',
      last_name: 'User',
      username: 'testuser',
      language_code: 'en',
      is_premium: true,
      photo_url: 'https://example.com/photo.jpg',
      allows_write_to_pm: true
    });
    expect(result.current.theme).toBe('light');
    expect(result.current.isExpanded).toBe(true);
    expect(result.current.viewportHeight).toBe(800);
  });

  it('should update theme when Telegram theme changes', () => {
    const { result } = renderHook(() => useTelegram());
    
    act(() => {
      // Update the theme in the mock
      mockTelegram.WebApp.colorScheme = 'dark';
      mockTelegram.WebApp.triggerEvent('themeChanged');
    });
    
    expect(result.current.theme).toBe('dark');
  });

  it('should call showAlert when showAlert is called', () => {
    const { result } = renderHook(() => useTelegram());
    const testMessage = 'Test alert message';
    
    act(() => {
      result.current.showAlert(testMessage);
    });

    expect(window.Telegram.WebApp.showAlert).toHaveBeenCalledWith(testMessage);
  });

  it('should call showConfirm when showConfirm is called', async () => {
    const { result } = renderHook(() => useTelegram());
    const testMessage = 'Test confirm message';
    
    const confirmed = await act(async () => {
      return result.current.showConfirm(testMessage);
    });

    expect(window.Telegram.WebApp.showConfirm).toHaveBeenCalledWith(
      testMessage, 
      expect.any(Function)
    );
    expect(confirmed).toBe(true);
  });

  it('should handle haptic feedback', () => {
    const { result } = renderHook(() => useTelegram());
    
    // Trigger haptic feedback
    result.current.haptic.impact('medium');
    result.current.haptic.notification('success');
    result.current.haptic.selection();
    
    // Verify the HapticFeedback methods were called
    expect(mockTelegram.WebApp.HapticFeedback.impactOccurred).toHaveBeenCalledWith('medium');
    expect(mockTelegram.WebApp.HapticFeedback.notificationOccurred).toHaveBeenCalledWith('success');
    expect(mockTelegram.WebApp.HapticFeedback.selectionChanged).toHaveBeenCalled();
  });

  it('should handle theme change event', () => {
    const { result } = renderHook(() => useTelegram());
    
    // Type assertion for the mock
    const mockOnEvent = window.Telegram?.WebApp.onEvent as jest.Mock;
    
    // Simulate theme change
    act(() => {
      const themeChangeHandler = mockOnEvent.mock.calls.find(
        ([eventName]: [string]) => eventName === 'themeChanged'
      )?.[1];
      if (themeChangeHandler) {
        themeChangeHandler();
      }
    });
    
    // Should update the theme
    expect(result.current.theme).toBe('light');
  });

  it('should clean up event listeners on unmount', () => {
    const { unmount } = renderHook(() => useTelegram());
    
    if (!window.Telegram) {
      throw new Error('Telegram is not defined');
    }
    
    // Get the mock implementation details
    const mockOnEvent = window.Telegram.WebApp.onEvent as jest.Mock;
    const mockOffEvent = window.Telegram.WebApp.offEvent as jest.Mock;
    
    // Verify events were registered
    expect(mockOnEvent).toHaveBeenCalledWith('themeChanged', expect.any(Function));
    expect(mockOnEvent).toHaveBeenCalledWith('viewportChanged', expect.any(Function));
    
    // Unmount the component
    unmount();
    
    // Verify cleanup was called for each event
    expect(mockOffEvent).toHaveBeenCalledTimes(2);
    expect(mockOffEvent).toHaveBeenCalledWith('themeChanged', expect.any(Function));
    expect(mockOffEvent).toHaveBeenCalledWith('viewportChanged', expect.any(Function));
  });
  
  it('should update viewport dimensions when they change', () => {
    const { result } = renderHook(() => useTelegram());
    
    act(() => {
      // Update viewport dimensions in the mock
      mockTelegram.WebApp.viewportHeight = 900;
      mockTelegram.WebApp.triggerEvent('viewportChanged', { 
        width: 400, 
        height: 900,
        isStateStable: true
      });
    });
    
    expect(result.current.viewportHeight).toBe(900);
  });

  it('should handle expand event', () => {
    const { result } = renderHook(() => useTelegram());
    
    if (!window.Telegram) {
      throw new Error('Telegram is not defined');
    }
    
    // Simulate expand event
    act(() => {
      window.Telegram.WebApp.isExpanded = true;
      mockTelegram.WebApp.triggerEvent('viewportChanged');
    });
    
    expect(result.current.isExpanded).toBe(true);
  });
  });