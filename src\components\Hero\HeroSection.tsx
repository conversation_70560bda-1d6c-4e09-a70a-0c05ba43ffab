import React from 'react';

interface HeroSectionProps {
  onGoShopping: () => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onGoShopping }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-100 via-green-200 to-green-300 flex items-center justify-center px-6 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 border-2 border-dashed border-green-700 rounded-full animate-pulse"></div>
        <div className="absolute bottom-32 right-16 w-24 h-24 border-2 border-dashed border-green-700 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 right-8 w-16 h-16 border-2 border-dashed border-green-700 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="text-center z-10 animate-fade-in">
        {/* Hero Text */}
        <div className="mb-16">
          <h1 className="text-6xl md:text-8xl font-black text-gray-900 leading-[0.9] mb-6 font-display tracking-tighter text-balance">
            The<br />
            Future<br />
            Looks<br />
            <span className="text-green-700">Bright</span>
          </h1>
        </div>

        {/* Hero Image */}
        <div className="mb-16 relative animate-slide-up" style={{ animationDelay: '0.3s' }}>
          <div className="w-80 h-80 mx-auto relative">
            <img
              src="https://images.pexels.com/photos/1031081/pexels-photo-1031081.jpeg?auto=compress&cs=tinysrgb&w=800"
              alt="Futuristic Sunglasses"
              className="w-full h-full object-cover rounded-4xl shadow-premium"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-green-400/10 to-transparent rounded-4xl"></div>
          </div>
        </div>

        {/* CTA Button */}
        <button
          onClick={onGoShopping}
          className="bg-green-700 hover:bg-green-800 text-white px-12 py-4 rounded-2xl text-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-premium hover:shadow-xl animate-scale-in"
          style={{ animationDelay: '0.6s' }}
        >
          Go Shopping
        </button>
      </div>
    </div>
  );
};

export default HeroSection;