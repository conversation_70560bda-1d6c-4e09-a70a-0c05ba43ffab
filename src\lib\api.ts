import axios from 'axios';
import { APP_CONFIG } from '../config/app';
import { ApiResponse } from '../types';

// Create axios instance with base URL and headers
const api = axios.create({
  baseURL: APP_CONFIG.API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Request interceptor for adding auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(APP_CONFIG.AUTH_TOKEN_KEY);
    if (token) {
      // Ensure headers object exists
      config.headers = config.headers || {};
      // Set the Authorization header
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error?.config;
    
    // If error is 401 and we haven't tried to refresh token yet
    if (error?.response?.status === 401 && originalRequest && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = localStorage.getItem(APP_CONFIG.REFRESH_TOKEN_KEY);
        if (refreshToken) {
          const response = await api.post<{ data: { accessToken: string } }>('/auth/refresh', { refreshToken });
          const { accessToken } = response.data.data;
          
          if (accessToken) {
            localStorage.setItem(APP_CONFIG.AUTH_TOKEN_KEY, accessToken);
            
            // Ensure headers exist and update the original request
            originalRequest.headers = originalRequest.headers || {};
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
            
            return api(originalRequest);
          }
        }
      } catch (refreshError) {
        // If refresh token fails, clear auth and redirect to login
        localStorage.removeItem(APP_CONFIG.AUTH_TOKEN_KEY);
        localStorage.removeItem(APP_CONFIG.REFRESH_TOKEN_KEY);
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

// Generic request function with type safety
export const apiRequest = async <T>(
  config: any
): Promise<ApiResponse<T>> => {
  try {
    const response = await api.request<ApiResponse<T>>(config);
    return response.data;
  } catch (error: any) {
    const errorMessage = error?.response?.data?.message || error?.message || 'An unknown error occurred';
    throw new Error(errorMessage);
  }
};

export default api;
