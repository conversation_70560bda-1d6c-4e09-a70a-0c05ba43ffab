import React, { useState } from 'react';
import { X, ChevronDown, ChevronUp, Star, Check } from 'lucide-react';
import { ProductFilter, ProductCategory } from '../../types/product';
import { useTelegram } from '../../hooks/useTelegram';

interface FilterPanelProps {
  filters: ProductFilter;
  onFiltersChange: (filters: ProductFilter) => void;
  onClose: () => void;
  availableCategories?: Array<{ category: ProductCategory; count: number }>;
  availableBrands?: Array<{ brand: string; count: number }>;
  priceRange?: { min: number; max: number };
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  onFiltersChange,
  onClose,
  availableCategories = [],
  availableBrands = [],
  priceRange = { min: 0, max: 1000 },
}) => {
  const { hapticFeedback } = useTelegram();
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    price: true,
    rating: true,
    brands: false,
    features: false,
  });

  const [localPriceRange, setLocalPriceRange] = useState({
    min: filters.priceRange?.min ?? priceRange.min,
    max: filters.priceRange?.max ?? priceRange.max,
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    hapticFeedback.light();
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const handleCategoryToggle = (category: ProductCategory) => {
    hapticFeedback.light();
    const currentCategories = filters.categories || [];
    const newCategories = currentCategories.includes(category)
      ? currentCategories.filter(c => c !== category)
      : [...currentCategories, category];
    
    onFiltersChange({
      ...filters,
      categories: newCategories.length > 0 ? newCategories : undefined,
    });
  };

  const handleBrandToggle = (brand: string) => {
    hapticFeedback.light();
    const currentBrands = filters.brands || [];
    const newBrands = currentBrands.includes(brand)
      ? currentBrands.filter(b => b !== brand)
      : [...currentBrands, brand];
    
    onFiltersChange({
      ...filters,
      brands: newBrands.length > 0 ? newBrands : undefined,
    });
  };

  const handlePriceRangeChange = () => {
    hapticFeedback.light();
    onFiltersChange({
      ...filters,
      priceRange: {
        min: localPriceRange.min,
        max: localPriceRange.max,
      },
    });
  };

  const handleRatingChange = (rating: number) => {
    hapticFeedback.light();
    onFiltersChange({
      ...filters,
      rating: filters.rating === rating ? undefined : rating,
    });
  };

  const handleFeatureToggle = (feature: keyof Pick<ProductFilter, 'inStock' | 'onSale' | 'isNew' | 'isFeatured'>) => {
    hapticFeedback.light();
    onFiltersChange({
      ...filters,
      [feature]: filters[feature] ? undefined : true,
    });
  };

  const clearAllFilters = () => {
    hapticFeedback.medium();
    onFiltersChange({});
    setLocalPriceRange({ min: priceRange.min, max: priceRange.max });
  };

  const activeFiltersCount = Object.keys(filters).length;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex">
      <div className="bg-white w-full max-w-sm ml-auto h-full overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-6 h-6 text-gray-600" />
          </button>
        </div>

        {/* Clear All */}
        {activeFiltersCount > 0 && (
          <div className="p-4 border-b border-gray-100">
            <button
              onClick={clearAllFilters}
              className="text-red-600 hover:text-red-700 font-medium"
            >
              Clear all filters ({activeFiltersCount})
            </button>
          </div>
        )}

        {/* Categories */}
        <div className="border-b border-gray-100">
          <button
            onClick={() => toggleSection('categories')}
            className="w-full flex items-center justify-between p-4 hover:bg-gray-50"
          >
            <span className="font-medium text-gray-900">Categories</span>
            {expandedSections.categories ? (
              <ChevronUp className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-500" />
            )}
          </button>
          {expandedSections.categories && (
            <div className="px-4 pb-4 space-y-2">
              {availableCategories.map(({ category, count }) => (
                <label
                  key={category}
                  className="flex items-center space-x-3 cursor-pointer"
                >
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={filters.categories?.includes(category) || false}
                      onChange={() => handleCategoryToggle(category)}
                      className="sr-only"
                    />
                    <div className={`w-5 h-5 border-2 rounded ${
                      filters.categories?.includes(category)
                        ? 'bg-green-500 border-green-500'
                        : 'border-gray-300'
                    } flex items-center justify-center`}>
                      {filters.categories?.includes(category) && (
                        <Check className="w-3 h-3 text-white" />
                      )}
                    </div>
                  </div>
                  <span className="text-gray-700 capitalize flex-1">
                    {category.replace('-', ' ')}
                  </span>
                  <span className="text-sm text-gray-500">({count})</span>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Price Range */}
        <div className="border-b border-gray-100">
          <button
            onClick={() => toggleSection('price')}
            className="w-full flex items-center justify-between p-4 hover:bg-gray-50"
          >
            <span className="font-medium text-gray-900">Price Range</span>
            {expandedSections.price ? (
              <ChevronUp className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-500" />
            )}
          </button>
          {expandedSections.price && (
            <div className="px-4 pb-4">
              <div className="space-y-4">
                <div className="flex space-x-2">
                  <div className="flex-1">
                    <label className="block text-sm text-gray-600 mb-1">Min</label>
                    <input
                      type="number"
                      value={localPriceRange.min}
                      onChange={(e) => setLocalPriceRange(prev => ({
                        ...prev,
                        min: parseFloat(e.target.value) || 0
                      }))}
                      onBlur={handlePriceRangeChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-sm text-gray-600 mb-1">Max</label>
                    <input
                      type="number"
                      value={localPriceRange.max}
                      onChange={(e) => setLocalPriceRange(prev => ({
                        ...prev,
                        max: parseFloat(e.target.value) || 1000
                      }))}
                      onBlur={handlePriceRangeChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                  </div>
                </div>
                <div className="text-sm text-gray-500">
                  ETB {localPriceRange.min} - ETB {localPriceRange.max}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Rating */}
        <div className="border-b border-gray-100">
          <button
            onClick={() => toggleSection('rating')}
            className="w-full flex items-center justify-between p-4 hover:bg-gray-50"
          >
            <span className="font-medium text-gray-900">Minimum Rating</span>
            {expandedSections.rating ? (
              <ChevronUp className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-500" />
            )}
          </button>
          {expandedSections.rating && (
            <div className="px-4 pb-4 space-y-2">
              {[4, 3, 2, 1].map((rating) => (
                <label
                  key={rating}
                  className="flex items-center space-x-3 cursor-pointer"
                >
                  <div className="relative">
                    <input
                      type="radio"
                      name="rating"
                      checked={filters.rating === rating}
                      onChange={() => handleRatingChange(rating)}
                      className="sr-only"
                    />
                    <div className={`w-5 h-5 border-2 rounded-full ${
                      filters.rating === rating
                        ? 'bg-green-500 border-green-500'
                        : 'border-gray-300'
                    } flex items-center justify-center`}>
                      {filters.rating === rating && (
                        <div className="w-2 h-2 bg-white rounded-full" />
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${
                          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                        }`}
                      />
                    ))}
                    <span className="text-gray-700 ml-2">& up</span>
                  </div>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Features */}
        <div className="border-b border-gray-100">
          <button
            onClick={() => toggleSection('features')}
            className="w-full flex items-center justify-between p-4 hover:bg-gray-50"
          >
            <span className="font-medium text-gray-900">Features</span>
            {expandedSections.features ? (
              <ChevronUp className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-500" />
            )}
          </button>
          {expandedSections.features && (
            <div className="px-4 pb-4 space-y-2">
              {[
                { key: 'inStock' as const, label: 'In Stock' },
                { key: 'onSale' as const, label: 'On Sale' },
                { key: 'isNew' as const, label: 'New Arrivals' },
                { key: 'isFeatured' as const, label: 'Featured' },
              ].map(({ key, label }) => (
                <label
                  key={key}
                  className="flex items-center space-x-3 cursor-pointer"
                >
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={filters[key] || false}
                      onChange={() => handleFeatureToggle(key)}
                      className="sr-only"
                    />
                    <div className={`w-5 h-5 border-2 rounded ${
                      filters[key]
                        ? 'bg-green-500 border-green-500'
                        : 'border-gray-300'
                    } flex items-center justify-center`}>
                      {filters[key] && (
                        <Check className="w-3 h-3 text-white" />
                      )}
                    </div>
                  </div>
                  <span className="text-gray-700">{label}</span>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Brands */}
        {availableBrands.length > 0 && (
          <div>
            <button
              onClick={() => toggleSection('brands')}
              className="w-full flex items-center justify-between p-4 hover:bg-gray-50"
            >
              <span className="font-medium text-gray-900">Brands</span>
              {expandedSections.brands ? (
                <ChevronUp className="w-5 h-5 text-gray-500" />
              ) : (
                <ChevronDown className="w-5 h-5 text-gray-500" />
              )}
            </button>
            {expandedSections.brands && (
              <div className="px-4 pb-4 space-y-2">
                {availableBrands.map(({ brand, count }) => (
                  <label
                    key={brand}
                    className="flex items-center space-x-3 cursor-pointer"
                  >
                    <div className="relative">
                      <input
                        type="checkbox"
                        checked={filters.brands?.includes(brand) || false}
                        onChange={() => handleBrandToggle(brand)}
                        className="sr-only"
                      />
                      <div className={`w-5 h-5 border-2 rounded ${
                        filters.brands?.includes(brand)
                          ? 'bg-green-500 border-green-500'
                          : 'border-gray-300'
                      } flex items-center justify-center`}>
                        {filters.brands?.includes(brand) && (
                          <Check className="w-3 h-3 text-white" />
                        )}
                      </div>
                    </div>
                    <span className="text-gray-700 flex-1">{brand}</span>
                    <span className="text-sm text-gray-500">({count})</span>
                  </label>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
