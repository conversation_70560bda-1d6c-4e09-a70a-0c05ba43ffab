import { useEffect, useState, useCallback, useMemo } from 'react';

type ThemeParams = {
  bg_color?: string;
  text_color?: string;
  hint_color?: string;
  link_color?: string;
  button_color?: string;
  button_text_color?: string;
  secondary_bg_color?: string;
  header_bg_color?: string;
  accent_text_color?: string;
  section_bg_color?: string;
  section_header_text_color?: string;
  subtitle_text_color?: string;
  destructive_text_color?: string;
};

type HapticFeedback = {
  impactOccurred: (style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft') => void;
  notificationOccurred: (type: 'error' | 'success' | 'warning') => void;
  selectionChanged: () => void;
};

type BackButton = {
  show: () => void;
  hide: () => void;
  onClick: (callback: () => void) => void;
  offClick: (callback: () => void) => void;
  showProgress: (show: boolean) => void;
};

type MainButton = {
  text: string;
  color: string;
  textColor: string;
  isVisible: boolean;
  isActive: boolean;
  isProgressVisible: boolean;
  setText: (text: string) => void;
  onClick: (callback: () => void) => void;
  offClick: (callback: () => void) => void;
  show: () => void;
  hide: () => void;
  enable: () => void;
  disable: () => void;
  showProgress: (leaveActive?: boolean) => void;
  hideProgress: () => void;
  setParams: (params: {
    color?: string;
    text_color?: string;
    is_active?: boolean;
    is_visible?: boolean;
    text?: string;
  }) => void;
};

type PopupButton = {
  id?: string;
  type?: 'default' | 'ok' | 'close' | 'cancel' | 'destructive';
  text: string;
};

type PopupParams = {
  title?: string;
  message: string;
  buttons?: PopupButton[];
};

interface TelegramWebApp {
  initData: string;
  initDataUnsafe: {
    user?: {
      id: number;
      first_name: string;
      last_name?: string;
      username?: string;
      language_code?: string;
      is_premium?: boolean;
      photo_url?: string;
      allows_write_to_pm?: boolean;
    };
    chat_type?: 'sender' | 'private' | 'group' | 'supergroup' | 'channel';
    chat_instance?: string;
    start_param?: string;
    can_send_after?: number;
    auth_date: number;
    hash: string;
  };
  version: string;
  platform: string;
  colorScheme: 'light' | 'dark';
  themeParams: ThemeParams;
  isExpanded: boolean;
  viewportHeight: number;
  viewportStableHeight: number;
  headerColor: string;
  backgroundColor: string;
  isClosingConfirmationEnabled: boolean;
  BackButton: BackButton;
  MainButton: MainButton;
  HapticFeedback: HapticFeedback;
  isVersionAtLeast: (version: string) => boolean;
  setHeaderColor: (color: string) => void;
  setBackgroundColor: (color: string) => void;
  enableClosingConfirmation: () => void;
  disableClosingConfirmation: () => void;
  onEvent: (eventType: string, eventHandler: () => void) => void;
  offEvent: (eventType: string, eventHandler: () => void) => void;
  sendData: (data: unknown) => void;
  switchInlineQuery: (query: string, choose_chat_types?: string[]) => void;
  openLink: (url: string, options?: { try_instant_view?: boolean }) => void;
  openTelegramLink: (url: string) => void;
  openInvoice: (url: string, callback?: (status: 'paid' | 'cancelled' | 'failed' | 'pending') => void) => void;
  showPopup: (params: PopupParams, callback?: (button_id: string | null) => void) => void;
  showAlert: (message: string, callback?: () => void) => void;
  showConfirm: (message: string, callback?: (confirmed: boolean) => void) => void;
  showScanQrPopup: (params: { text?: string }, callback: (text: string) => void) => void;
  closeScanQrPopup: () => void;
  readTextFromClipboard: (callback: (text: string | null) => void) => void;
  requestWriteAccess: (callback?: (accessGranted: boolean) => void) => void;
  requestContact: (callback: (phoneNumber: string) => void) => void;
  ready: () => void;
  expand: () => void;
  close: () => void;
}

declare global {
  interface Window {
    Telegram?: {
      WebApp: TelegramWebApp;
    };
  }
}

export const useTelegram = () => {
  const [tg, setTg] = useState<TelegramWebApp | null>(null);
  const [user, setUser] = useState<{
    id: number;
    first_name: string;
    last_name?: string;
    username?: string;
    language_code?: string;
    is_premium?: boolean;
    photo_url?: string;
    allows_write_to_pm?: boolean;
  } | null>(null);
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [viewportHeight, setViewportHeight] = useState<number>(0);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  useEffect(() => {
    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
      const webApp = window.Telegram.WebApp;
      setTg(webApp);
      
      // Initialize user data
      if (webApp.initDataUnsafe.user) {
        const { user } = webApp.initDataUnsafe;
        setUser({
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          username: user.username,
          language_code: user.language_code,
          is_premium: user.is_premium,
          photo_url: user.photo_url,
          allows_write_to_pm: user.allows_write_to_pm,
        });
      }

      // Set theme
      setTheme(webApp.colorScheme);
      
      // Set viewport height
      setViewportHeight(webApp.viewportHeight);
      setIsExpanded(webApp.isExpanded);
      
      // Handle viewport changes
      const onViewportChanged = () => {
        setViewportHeight(webApp.viewportHeight);
        setIsExpanded(webApp.isExpanded);
      };
      
      webApp.onEvent('viewportChanged', onViewportChanged);

      const onThemeChanged = () => {
        setTheme(webApp.colorScheme);
      };

      webApp.onEvent('themeChanged', onThemeChanged);
      
      // Cleanup
      return () => {
        webApp.offEvent('viewportChanged', onViewportChanged);
        webApp.offEvent('themeChanged', onThemeChanged);
      };
    }
  }, []);

  // Alert and confirmation dialogs
  const showAlert = useCallback((message: string) => {
    if (tg) {
      tg.showAlert(message);
    }
  }, [tg]);

  const showConfirm = useCallback((message: string): Promise<boolean> => {
    return new Promise((resolve) => {
      if (tg) {
        tg.showConfirm(message, (confirmed: boolean) => {
          resolve(confirmed);
        });
      } else {
        resolve(false);
      }
    });
  }, [tg]);

  // Haptic feedback
  const haptic = useMemo(() => ({
    impact: (style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft') => {
      tg?.HapticFeedback.impactOccurred(style);
    },
    notification: (type: 'error' | 'success' | 'warning') => {
      tg?.HapticFeedback.notificationOccurred(type);
    },
    selection: () => {
      tg?.HapticFeedback.selectionChanged();
    },
  }), [tg]);

  return {
    // State
    tg,
    user,
    theme,
    viewportHeight,
    isExpanded,
    
    // Dialogs
    showAlert,
    showConfirm,
    
    // Haptic feedback
    haptic,
  };
};