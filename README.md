# Souq - Telegram Mini App E-commerce Platform

A Telegram Mini App-based e-commerce platform tailored for Ethiopia's market, focusing on solving key e-commerce challenges through a Telegram-native experience.

## Features

- 🛍️ **Product Catalog**: Browse and search products with filters
- 🛒 **Shopping Cart**: Add/remove items and manage quantities
- 💳 **Secure Payments**: Telebirr integration with escrow support
- 🚚 **Smart Logistics**: Landmark-based delivery system
- 📱 **Mobile-First**: Optimized for mobile devices
- 🌐 **Bilingual**: English and Amharic support
- 🔒 **Secure**: End-to-end encryption and secure authentication

## Tech Stack

- **Frontend**:
  - React 18 with TypeScript
  - Vite
  - Tailwind CSS
  - React Query
  - React Hook Form
  - i18next
  - Framer Motion

- **Backend**:
  - Node.js with NestJS
  - MongoDB
  - Redis
  - WebSockets

- **DevOps**:
  - Docker
  - GitHub Actions
  - ESLint + Prettier
  - Husky

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Git
- MongoDB
- Redis

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-username/souq-telegram-app.git
   cd souq-telegram-app
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Update the environment variables in .env
   ```

4. Start the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── assets/           # Static assets (images, fonts, icons)
├── components/       # Reusable UI components
│   ├── common/       # Generic components (buttons, inputs, etc.)
│   ├── layout/       # Layout components
│   └── ui/           # UI kit components
├── features/         # Feature-based modules
│   ├── auth/         # Authentication
│   ├── products/     # Product catalog
│   ├── cart/         # Shopping cart
│   ├── checkout/     # Checkout process
│   └── account/      # User account
├── hooks/            # Custom hooks
├── lib/              # Third-party library configurations
├── services/         # API service layer
├── stores/           # State management
├── types/            # TypeScript type definitions
└── utils/            # Utility functions
```

## Development

### Available Scripts

- `dev` - Start development server
- `build` - Build for production
- `preview` - Preview production build
- `lint` - Lint code
- `format` - Format code
- `test` - Run tests
- `test:watch` - Run tests in watch mode
- `test:coverage` - Generate test coverage

### Code Style

- Use TypeScript for type safety
- Follow the Airbnb JavaScript Style Guide
- Use functional components with hooks
- Use absolute imports
- Write meaningful commit messages

## Deployment

### Production Build

```bash
npm run build
# or
yarn build
```

### Docker

```bash
# Build the Docker image
docker build -t souq-app .

# Run the container
docker run -p 3000:3000 souq-app
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [Telegram WebApp API](https://core.telegram.org/bots/webapps)
- [Vite](https://vitejs.dev/)
- [React](https://reactjs.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [NestJS](https://nestjs.com/)
